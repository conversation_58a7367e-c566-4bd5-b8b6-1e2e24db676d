# Existing entries
*.docx
*.pdf

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# Data Science & ML
*.pkl
*.joblib
*.h5
*.hdf5
models/
data/processed/
data/features/
data/labeled/
data/results/
data/predictions/
data/analysis/

# Generated files from your project
tfidf_vectorizer.pkl
label_encoder.pkl
model_naive_bayes.pkl
model_random_forest.pkl
model_xgboost.pkl
lexicons/
utils/__pycache__/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Excel temporary files
~$*.xlsx
~$*.xls

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
