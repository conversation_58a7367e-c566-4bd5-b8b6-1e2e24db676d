import{s as i,r as e,j as r,a as d,P as m,b as p,F as P}from"./index.BTGIlECR.js";const x=i("div",{target:"e1swy67l0"})({"@media print":{display:"none"}}),y=({className:t,scriptRunId:a,numParticles:s,numParticleTypes:o,ParticleComponent:n})=>r(x,{className:t,"data-testid":t,children:d(s).map(l=>{const c=Math.floor(Math.random()*o);return r(n,{particleType:c},a+l)})}),E=e.memo(y),f=({children:t})=>{const a=e.useContext(m)?.();return a?p.createPortal(t,a):r(P,{children:t})};export{E as P,f as R};
