{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 📊 09. Model Evaluation & Ensemble Methods\n",
    "\n",
    "**Tujuan**: <PERSON><PERSON><PERSON> komprehensif dan ensemble methods untuk sentiment analysis\n",
    "\n",
    "**Key Features**:\n",
    "1. 🔄 **Model Loading** - Load trained RF & XGBoost models\n",
    "2. 📊 **Comprehensive Evaluation** - Advanced metrics & analysis\n",
    "3. 🎭 **Ensemble Methods** - Voting, stacking, blending\n",
    "4. 📈 **Performance Visualization** - ROC, PR curves, confusion matrices\n",
    "5. 🔍 **Error Analysis** - Misclassification patterns\n",
    "6. 🏆 **Final Model Selection** - Best performing approach\n",
    "\n",
    "**Input**: Trained models + test data  \n",
    "**Output**: Final production model dengan comprehensive evaluation\n",
    "\n",
    "---"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🛠️ Setup & Import Libraries"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Core libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import plotly.express as px\n",
    "import plotly.graph_objects as go\n",
    "from plotly.subplots import make_subplots\n",
    "import warnings\n",
    "from datetime import datetime\n",
    "import json\n",
    "import joblib\n",
    "import os\n",
    "from collections import defaultdict\n",
    "\n",
    "# Machine learning libraries\n",
    "from sklearn.ensemble import VotingClassifier, StackingClassifier\n",
    "from sklearn.linear_model import LogisticRegression\n",
    "from sklearn.model_selection import cross_val_score, StratifiedKFold\n",
    "from sklearn.metrics import (\n",
    "    classification_report, confusion_matrix, accuracy_score,\n",
    "    precision_recall_fscore_support, roc_auc_score, roc_curve,\n",
    "    precision_recall_curve, matthews_corrcoef, cohen_kappa_score,\n",
    "    average_precision_score, log_loss, brier_score_loss\n",
    ")\n",
    "from sklearn.calibration import calibration_curve, CalibratedClassifierCV\n",
    "from sklearn.preprocessing import LabelEncoder\n",
    "import scipy.stats as stats\n",
    "\n",
    "# Statistical testing\n",
    "from scipy.stats import mcnemar, chi2_contingency\n",
    "\n",
    "# Advanced analysis\n",
    "try:\n",
    "    import shap\n",
    "    SHAP_AVAILABLE = True\n",
    "except ImportError:\n",
    "    print(\"⚠️ SHAP not available. Using standard feature importance.\")\n",
    "    SHAP_AVAILABLE = False\n",
    "\n",
    "# Configuration\n",
    "warnings.filterwarnings('ignore')\n",
    "plt.style.use('seaborn-v0_8')\n",
    "pd.set_option('display.max_columns', None)\n",
    "np.random.seed(42)\n",
    "\n",
    "print(f\"📊 Model evaluation libraries imported successfully!\")\n",
    "print(f\"  • SHAP analysis: {'Available' if SHAP_AVAILABLE else 'Not available'}\")\n",
    "print(f\"🕐 Model evaluation started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📂 Load Models & Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load trained models and data\n",
    "print(\"📂 LOADING MODELS & DATA\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Load Random Forest model and results\n",
    "try:\n",
    "    rf_artifacts = joblib.load('models/random_forest/rf_production_artifacts.pkl')\n",
    "    rf_model = rf_artifacts['model']\n",
    "    \n",
    "    with open('data/results/random_forest_results.json', 'r') as f:\n",
    "        rf_results = json.load(f)\n",
    "    \n",
    "    with open('data/predictions/rf_predictions.json', 'r') as f:\n",
    "        rf_predictions = json.load(f)\n",
    "    \n",
    "    print(f\"✅ Random Forest model loaded\")\n",
    "    print(f\"  • Test accuracy: {rf_results['best_model_results']['metrics']['test']['accuracy']:.3f}\")\n",
    "    rf_available = True\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(f\"⚠️ Random Forest model not found\")\n",
    "    rf_available = False\n",
    "\n",
    "# Load XGBoost model and results\n",
    "try:\n",
    "    xgb_artifacts = joblib.load('models/xgboost/xgb_production_artifacts.pkl')\n",
    "    xgb_model = xgb_artifacts['model']\n",
    "    \n",
    "    with open('data/results/xgboost_results.json', 'r') as f:\n",
    "        xgb_results = json.load(f)\n",
    "    \n",
    "    with open('data/predictions/xgb_predictions.json', 'r') as f:\n",
    "        xgb_predictions = json.load(f)\n",
    "    \n",
    "    print(f\"✅ XGBoost model loaded\")\n",
    "    print(f\"  • Test accuracy: {xgb_results['best_model_results']['metrics']['test']['accuracy']:.3f}\")\n",
    "    xgb_available = True\n",
    "    \n",
    "except FileNotFoundError:\n",
    "    print(f\"⚠️ XGBoost model not found\")\n",
    "    xgb_available = False\n",
    "\n",
    "if not rf_available and not xgb_available:\n",
    "    print(f\"❌ No trained models found. Please run model training first.\")\n",
    "    raise FileNotFoundError(\"Trained models required for evaluation\")\n",
    "\n",
    "# Load feature data and labels\n",
    "try:\n",
    "    # Use the same artifacts from one of the models\n",
    "    if rf_available:\n",
    "        vectorizer = rf_artifacts['vectorizer']\n",
    "        feature_names = rf_artifacts['feature_names']\n",
    "        class_names = rf_artifacts['class_names']\n",
    "        labeling_method = rf_artifacts['preprocessing_info']['labeling_method']\n",
    "    else:\n",
    "        vectorizer = xgb_artifacts['vectorizer']\n",
    "        feature_names = xgb_artifacts['feature_names']\n",
    "        class_names = xgb_artifacts['class_names']\n",
    "        labeling_method = xgb_artifacts['preprocessing_info']['labeling_method']\n",
    "    \n",
    "    print(f\"✅ Model artifacts loaded\")\n",
    "    print(f\"  • Features: {len(feature_names)}\")\n",
    "    print(f\"  • Classes: {class_names}\")\n",
    "    print(f\"  • Labeling method: {labeling_method}\")\n",
    "    \n",
    "except Exception as e:\n",
    "    print(f\"⚠️ Error loading artifacts: {e}\")\n",
    "\n",
    "# Reconstruct test data from predictions\n",
    "if rf_available:\n",
    "    y_test_true = np.array(rf_predictions['y_test_true'])\n",
    "    rf_y_test_pred = np.array(rf_predictions['y_test_pred'])\n",
    "    rf_y_test_proba = np.array(rf_predictions['y_test_proba'])\n",
    "else:\n",
    "    y_test_true = np.array(xgb_predictions['y_test_true'])\n",
    "    rf_y_test_pred = None\n",
    "    rf_y_test_proba = None\n",
    "\n",
    "if xgb_available:\n",
    "    xgb_y_test_pred = np.array(xgb_predictions['y_test_pred'])\n",
    "    xgb_y_test_proba = np.array(xgb_predictions['y_test_proba'])\n",
    "else:\n",
    "    xgb_y_test_pred = None\n",
    "    xgb_y_test_proba = None\n",
    "\n",
    "print(f\"\\n📊 Test Data Summary:\")\n",
    "print(f\"  • Test samples: {len(y_test_true)}\")\n",
    "print(f\"  • True label distribution: {dict(zip(*np.unique(y_test_true, return_counts=True)))}\")\n",
    "print(f\"  • Random Forest predictions: {'Available' if rf_available else 'Not available'}\")\n",
    "print(f\"  • XGBoost predictions: {'Available' if xgb_available else 'Not available'}\")\n",
    "\n",
    "# Determine evaluation scope\n",
    "both_models_available = rf_available and xgb_available\n",
    "print(f\"\\n🎯 Evaluation Scope:\")\n",
    "if both_models_available:\n",
    "    print(f\"  • Individual model evaluation\")\n",
    "    print(f\"  • Model comparison analysis\")\n",
    "    print(f\"  • Ensemble methods\")\n",
    "    print(f\"  • Statistical significance testing\")\n",
    "else:\n",
    "    print(f\"  • Single model evaluation only\")\n",
    "    print(f\"  • Limited ensemble options\")\n",
    "\n",
    "print(f\"\\n✅ Models and data loaded successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Comprehensive Model Evaluation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Comprehensive evaluation of individual models\n",
    "print(\"📊 COMPREHENSIVE MODEL EVALUATION\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "def comprehensive_evaluation(y_true, y_pred, y_proba, model_name):\n",
    "    \"\"\"Perform comprehensive model evaluation\"\"\"\n",
    "    results = {}\n",
    "    \n",
    "    # Basic classification metrics\n",
    "    results['accuracy'] = accuracy_score(y_true, y_pred)\n",
    "    precision, recall, f1, support = precision_recall_fscore_support(y_true, y_pred, average='weighted')\n",
    "    results['precision'] = precision\n",
    "    results['recall'] = recall\n",
    "    results['f1'] = f1\n",
    "    \n",
    "    # Advanced metrics\n",
    "    results['kappa'] = cohen_kappa_score(y_true, y_pred)\n",
    "    results['mcc'] = matthews_corrcoef(y_true, y_pred)\n",
    "    \n",
    "    # Probabilistic metrics\n",
    "    if y_proba is not None:\n",
    "        results['log_loss'] = log_loss(y_true, y_proba)\n",
    "        \n",
    "        # For binary classification\n",
    "        if len(np.unique(y_true)) == 2:\n",
    "            if len(y_proba.shape) > 1 and y_proba.shape[1] > 1:\n",
    "                y_proba_pos = y_proba[:, 1]\n",
    "            else:\n",
    "                y_proba_pos = y_proba\n",
    "            \n",
    "            results['roc_auc'] = roc_auc_score(y_true, y_proba_pos)\n",
    "            results['avg_precision'] = average_precision_score(y_true, y_proba_pos)\n",
    "            results['brier_score'] = brier_score_loss(y_true, y_proba_pos)\n",
    "        \n",
    "        # For multiclass\n",
    "        else:\n",
    "            try:\n",
    "                results['roc_auc'] = roc_auc_score(y_true, y_proba, multi_class='ovr', average='weighted')\n",
    "            except:\n",
    "                results['roc_auc'] = None\n",
    "    \n",
    "    # Per-class metrics\n",
    "    per_class_precision, per_class_recall, per_class_f1, per_class_support = precision_recall_fscore_support(\n",
    "        y_true, y_pred, average=None\n",
    "    )\n",
    "    \n",
    "    results['per_class'] = {\n",
    "        'precision': per_class_precision.tolist(),\n",
    "        'recall': per_class_recall.tolist(),\n",
    "        'f1': per_class_f1.tolist(),\n",
    "        'support': per_class_support.tolist()\n",
    "    }\n",
    "    \n",
    "    # Confusion matrix\n",
    "    results['confusion_matrix'] = confusion_matrix(y_true, y_pred).tolist()\n",
    "    \n",
    "    return results\n",
    "\n",
    "# Evaluate Random Forest\n",
    "if rf_available:\n",
    "    print(f\"\\n🌲 RANDOM FOREST EVALUATION:\")\n",
    "    rf_eval = comprehensive_evaluation(y_test_true, rf_y_test_pred, rf_y_test_proba, \"Random Forest\")\n",
    "    \n",
    "    print(f\"  • Accuracy: {rf_eval['accuracy']:.4f}\")\n",
    "    print(f\"  • F1-Score: {rf_eval['f1']:.4f}\")\n",
    "    print(f\"  • Precision: {rf_eval['precision']:.4f}\")\n",
    "    print(f\"  • Recall: {rf_eval['recall']:.4f}\")\n",
    "    print(f\"  • Cohen's Kappa: {rf_eval['kappa']:.4f}\")\n",
    "    print(f\"  • Matthews Correlation: {rf_eval['mcc']:.4f}\")\n",
    "    if 'roc_auc' in rf_eval and rf_eval['roc_auc']:\n",
    "        print(f\"  • ROC AUC: {rf_eval['roc_auc']:.4f}\")\n",
    "    if 'log_loss' in rf_eval:\n",
    "        print(f\"  • Log Loss: {rf_eval['log_loss']:.4f}\")\n",
    "\n",
    "# Evaluate XGBoost\n",
    "if xgb_available:\n",
    "    print(f\"\\n🚀 XGBOOST EVALUATION:\")\n",
    "    xgb_eval = comprehensive_evaluation(y_test_true, xgb_y_test_pred, xgb_y_test_proba, \"XGBoost\")\n",
    "    \n",
    "    print(f\"  • Accuracy: {xgb_eval['accuracy']:.4f}\")\n",
    "    print(f\"  • F1-Score: {xgb_eval['f1']:.4f}\")\n",
    "    print(f\"  • Precision: {xgb_eval['precision']:.4f}\")\n",
    "    print(f\"  • Recall: {xgb_eval['recall']:.4f}\")\n",
    "    print(f\"  • Cohen's Kappa: {xgb_eval['kappa']:.4f}\")\n",
    "    print(f\"  • Matthews Correlation: {xgb_eval['mcc']:.4f}\")\n",
    "    if 'roc_auc' in xgb_eval and xgb_eval['roc_auc']:\n",
    "        print(f\"  • ROC AUC: {xgb_eval['roc_auc']:.4f}\")\n",
    "    if 'log_loss' in xgb_eval:\n",
    "        print(f\"  • Log Loss: {xgb_eval['log_loss']:.4f}\")\n",
    "\n",
    "# Statistical significance testing (if both models available)\n",
    "if both_models_available:\n",
    "    print(f\"\\n📈 STATISTICAL SIGNIFICANCE TESTING:\")\n",
    "    \n",
    "    # McNemar's test for paired predictions\n",
    "    rf_correct = (rf_y_test_pred == y_test_true)\n",
    "    xgb_correct = (xgb_y_test_pred == y_test_true)\n",
    "    \n",
    "    # Create contingency table\n",
    "    both_correct = np.sum(rf_correct & xgb_correct)\n",
    "    rf_correct_xgb_wrong = np.sum(rf_correct & ~xgb_correct)\n",
    "    rf_wrong_xgb_correct = np.sum(~rf_correct & xgb_correct)\n",
    "    both_wrong = np.sum(~rf_correct & ~xgb_correct)\n",
    "    \n",
    "    print(f\"  • Both correct: {both_correct}\")\n",
    "    print(f\"  • RF correct, XGB wrong: {rf_correct_xgb_wrong}\")\n",
    "    print(f\"  • RF wrong, XGB correct: {rf_wrong_xgb_correct}\")\n",
    "    print(f\"  • Both wrong: {both_wrong}\")\n",
    "    \n",
    "    # McNemar's test\n",
    "    if rf_correct_xgb_wrong + rf_wrong_xgb_correct > 0:\n",
    "        mcnemar_table = [[both_correct, rf_correct_xgb_wrong],\n",
    "                        [rf_wrong_xgb_correct, both_wrong]]\n",
    "        \n",
    "        try:\n",
    "            mcnemar_stat, mcnemar_p = mcnemar(mcnemar_table, exact=False, correction=True)\n",
    "            print(f\"  • McNemar's test statistic: {mcnemar_stat:.4f}\")\n",
    "            print(f\"  • McNemar's p-value: {mcnemar_p:.4f}\")\n",
    "            \n",
    "            if mcnemar_p < 0.05:\n",
    "                better_model = \"XGBoost\" if rf_wrong_xgb_correct > rf_correct_xgb_wrong else \"Random Forest\"\n",
    "                print(f\"  • Significant difference: {better_model} is significantly better (p < 0.05)\")\n",
    "            else:\n",
    "                print(f\"  • No significant difference between models (p >= 0.05)\")\n",
    "        except Exception as e:\n",
    "            print(f\"  • McNemar's test failed: {e}\")\n",
    "    \n",
    "    # Agreement analysis\n",
    "    agreement = np.sum(rf_y_test_pred == xgb_y_test_pred) / len(y_test_true)\n",
    "    print(f\"  • Model agreement: {agreement:.4f} ({agreement*100:.1f}%)\")\n",
    "\n",
    "print(f\"\\n✅ Comprehensive evaluation completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎭 Ensemble Methods"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Implement ensemble methods if both models are available\n",
    "print(\"🎭 ENSEMBLE METHODS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "if both_models_available:\n",
    "    # Load feature matrices for ensemble training\n",
    "    try:\n",
    "        # Try to load the same features used in training\n",
    "        X_features = joblib.load('data/features/X_optimized.pkl')\n",
    "        if hasattr(X_features, 'toarray'):\n",
    "            X_features = X_features.toarray()\n",
    "        print(f\"✅ Feature matrix loaded: {X_features.shape}\")\n",
    "        \n",
    "        # Recreate the same train/test split\n",
    "        from sklearn.model_selection import train_test_split\n",
    "        \n",
    "        # Get labels from one of the models\n",
    "        if rf_available:\n",
    "            y_all = np.concatenate([rf_predictions['y_train_true'], \n",
    "                                   rf_predictions['y_val_true'], \n",
    "                                   rf_predictions['y_test_true']])\n",
    "        else:\n",
    "            y_all = np.concatenate([xgb_predictions['y_train_true'], \n",
    "                                   xgb_predictions['y_val_true'], \n",
    "                                   xgb_predictions['y_test_true']])\n",
    "        \n",
    "        # Split data consistently\n",
    "        X_temp, X_test, y_temp, y_test = train_test_split(\n",
    "            X_features, y_all, test_size=0.2, random_state=42, stratify=y_all\n",
    "        )\n",
    "        X_train, X_val, y_train, y_val = train_test_split(\n",
    "            X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp\n",
    "        )\n",
    "        \n",
    "        features_available = True\n",
    "        print(f\"✅ Data split recreated: Train {len(X_train)}, Val {len(X_val)}, Test {len(X_test)}\")\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"⚠️ Could not load features for ensemble training: {e}\")\n",
    "        features_available = False\n",
    "    \n",
    "    if features_available:\n",
    "        print(f\"\\n🔄 Training ensemble methods...\")\n",
    "        \n",
    "        # 1. Simple Voting Classifier\n",
    "        print(f\"\\n1️⃣ VOTING CLASSIFIER:\")\n",
    "        \n",
    "        # Hard voting\n",
    "        voting_hard = VotingClassifier(\n",
    "            estimators=[('rf', rf_model), ('xgb', xgb_model)],\n",
    "            voting='hard'\n",
    "        )\n",
    "        voting_hard.fit(X_train, y_train)\n",
    "        \n",
    "        # Soft voting\n",
    "        voting_soft = VotingClassifier(\n",
    "            estimators=[('rf', rf_model), ('xgb', xgb_model)],\n",
    "            voting='soft'\n",
    "        )\n",
    "        voting_soft.fit(X_train, y_train)\n",
    "        \n",
    "        # Evaluate voting classifiers\n",
    "        voting_hard_pred = voting_hard.predict(X_test)\n",
    "        voting_hard_proba = voting_hard.predict_proba(X_test)\n",
    "        voting_hard_eval = comprehensive_evaluation(y_test, voting_hard_pred, voting_hard_proba, \"Voting Hard\")\n",
    "        \n",
    "        voting_soft_pred = voting_soft.predict(X_test)\n",
    "        voting_soft_proba = voting_soft.predict_proba(X_test)\n",
    "        voting_soft_eval = comprehensive_evaluation(y_test, voting_soft_pred, voting_soft_proba, \"Voting Soft\")\n",
    "        \n",
    "        print(f\"  • Hard Voting - Accuracy: {voting_hard_eval['accuracy']:.4f}, F1: {voting_hard_eval['f1']:.4f}\")\n",
    "        print(f\"  • Soft Voting - Accuracy: {voting_soft_eval['accuracy']:.4f}, F1: {voting_soft_eval['f1']:.4f}\")\n",
    "        \n",
    "        # 2. Stacking Classifier\n",
    "        print(f\"\\n2️⃣ STACKING CLASSIFIER:\")\n",
    "        \n",
    "        # Use logistic regression as meta-learner\n",
    "        stacking_clf = StackingClassifier(\n",
    "            estimators=[('rf', rf_model), ('xgb', xgb_model)],\n",
    "            final_estimator=LogisticRegression(random_state=42, max_iter=1000),\n",
    "            cv=3,\n",
    "            stack_method='predict_proba'\n",
    "        )\n",
    "        \n",
    "        stacking_clf.fit(X_train, y_train)\n",
    "        \n",
    "        stacking_pred = stacking_clf.predict(X_test)\n",
    "        stacking_proba = stacking_clf.predict_proba(X_test)\n",
    "        stacking_eval = comprehensive_evaluation(y_test, stacking_pred, stacking_proba, \"Stacking\")\n",
    "        \n",
    "        print(f\"  • Stacking - Accuracy: {stacking_eval['accuracy']:.4f}, F1: {stacking_eval['f1']:.4f}\")\n",
    "        \n",
    "        # 3. Weighted Average Ensemble\n",
    "        print(f\"\\n3️⃣ WEIGHTED AVERAGE ENSEMBLE:\")\n",
    "        \n",
    "        # Calculate weights based on validation performance\n",
    "        rf_val_f1 = rf_eval['f1']\n",
    "        xgb_val_f1 = xgb_eval['f1']\n",
    "        \n",
    "        total_f1 = rf_val_f1 + xgb_val_f1\n",
    "        rf_weight = rf_val_f1 / total_f1\n",
    "        xgb_weight = xgb_val_f1 / total_f1\n",
    "        \n",
    "        print(f\"  • RF weight: {rf_weight:.3f}, XGB weight: {xgb_weight:.3f}\")\n",
    "        \n",
    "        # Weighted average of probabilities\n",
    "        weighted_proba = rf_weight * rf_y_test_proba + xgb_weight * xgb_y_test_proba\n",
    "        weighted_pred = np.argmax(weighted_proba, axis=1)\n",
    "        \n",
    "        weighted_eval = comprehensive_evaluation(y_test, weighted_pred, weighted_proba, \"Weighted Average\")\n",
    "        print(f\"  • Weighted Average - Accuracy: {weighted_eval['accuracy']:.4f}, F1: {weighted_eval['f1']:.4f}\")\n",
    "        \n",
    "        # 4. Simple Average Ensemble\n",
    "        print(f\"\\n4️⃣ SIMPLE AVERAGE ENSEMBLE:\")\n",
    "        \n",
    "        simple_avg_proba = (rf_y_test_proba + xgb_y_test_proba) / 2\n",
    "        simple_avg_pred = np.argmax(simple_avg_proba, axis=1)\n",
    "        \n",
    "        simple_avg_eval = comprehensive_evaluation(y_test, simple_avg_pred, simple_avg_proba, \"Simple Average\")\n",
    "        print(f\"  • Simple Average - Accuracy: {simple_avg_eval['accuracy']:.4f}, F1: {simple_avg_eval['f1']:.4f}\")\n",
    "        \n",
    "        # Store ensemble results\n",
    "        ensemble_results = {\n",
    "            'voting_hard': {\n",
    "                'model': voting_hard,\n",
    "                'predictions': voting_hard_pred.tolist(),\n",
    "                'probabilities': voting_hard_proba.tolist(),\n",
    "                'evaluation': voting_hard_eval\n",
    "            },\n",
    "            'voting_soft': {\n",
    "                'model': voting_soft,\n",
    "                'predictions': voting_soft_pred.tolist(),\n",
    "                'probabilities': voting_soft_proba.tolist(),\n",
    "                'evaluation': voting_soft_eval\n",
    "            },\n",
    "            'stacking': {\n",
    "                'model': stacking_clf,\n",
    "                'predictions': stacking_pred.tolist(),\n",
    "                'probabilities': stacking_proba.tolist(),\n",
    "                'evaluation': stacking_eval\n",
    "            },\n",
    "            'weighted_average': {\n",
    "                'weights': {'rf': rf_weight, 'xgb': xgb_weight},\n",
    "                'predictions': weighted_pred.tolist(),\n",
    "                'probabilities': weighted_proba.tolist(),\n",
    "                'evaluation': weighted_eval\n",
    "            },\n",
    "            'simple_average': {\n",
    "                'predictions': simple_avg_pred.tolist(),\n",
    "                'probabilities': simple_avg_proba.tolist(),\n",
    "                'evaluation': simple_avg_eval\n",
    "            }\n",
    "        }\n",
    "        \n",
    "        # Compare all methods\n",
    "        print(f\"\\n📊 ENSEMBLE COMPARISON:\")\n",
    "        print(f\"{'Method':<20} {'Accuracy':<10} {'F1-Score':<10} {'Precision':<10} {'Recall':<10}\")\n",
    "        print(\"-\" * 70)\n",
    "        \n",
    "        methods = {\n",
    "            'Random Forest': rf_eval,\n",
    "            'XGBoost': xgb_eval,\n",
    "            'Voting Hard': voting_hard_eval,\n",
    "            'Voting Soft': voting_soft_eval,\n",
    "            'Stacking': stacking_eval,\n",
    "            'Weighted Avg': weighted_eval,\n",
    "            'Simple Avg': simple_avg_eval\n",
    "        }\n",
    "        \n",
    "        best_method = None\n",
    "        best_f1 = 0\n",
    "        \n",
    "        for method_name, evaluation in methods.items():\n",
    "            acc = evaluation['accuracy']\n",
    "            f1 = evaluation['f1']\n",
    "            prec = evaluation['precision']\n",
    "            rec = evaluation['recall']\n",
    "            \n",
    "            print(f\"{method_name:<20} {acc:<10.4f} {f1:<10.4f} {prec:<10.4f} {rec:<10.4f}\")\n",
    "            \n",
    "            if f1 > best_f1:\n",
    "                best_f1 = f1\n",
    "                best_method = method_name\n",
    "        \n",
    "        print(f\"\\n🏆 Best performing method: {best_method} (F1: {best_f1:.4f})\")\n",
    "        \n",
    "    else:\n",
    "        print(f\"\\n⚠️ Ensemble methods skipped due to missing features\")\n",
    "        ensemble_results = None\n",
    "        best_method = \"XGBoost\" if xgb_eval['f1'] > rf_eval['f1'] else \"Random Forest\"\n",
    "\nelse:\n",
    "    print(f\"\\n⚠️ Ensemble methods require both Random Forest and XGBoost models\")\n",
    "    ensemble_results = None\n",
    "    \n",
    "    if rf_available:\n",
    "        best_method = \"Random Forest\"\n",
    "    else:\n",
    "        best_method = \"XGBoost\"\n",
    "\nprint(f\"\\n✅ Ensemble methods evaluation completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔍 Error Analysis & Misclassification Patterns"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Detailed error analysis and misclassification patterns\n",
    "print(\"🔍 ERROR ANALYSIS & MISCLASSIFICATION PATTERNS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "def analyze_errors(y_true, y_pred, y_proba, model_name):\n",
    "    \"\"\"Analyze prediction errors and patterns\"\"\"\n",
    "    error_analysis = {}\n",
    "    \n",
    "    # Basic error statistics\n",
    "    correct_predictions = y_true == y_pred\n",
    "    error_analysis['total_errors'] = np.sum(~correct_predictions)\n",
    "    error_analysis['error_rate'] = np.mean(~correct_predictions)\n",
    "    \n",
    "    # Confidence analysis\n",
    "    if y_proba is not None:\n",
    "        max_proba = np.max(y_proba, axis=1)\n",
    "        \n",
    "        # Errors by confidence level\n",
    "        high_conf_mask = max_proba >= 0.8\n",
    "        med_conf_mask = (max_proba >= 0.6) & (max_proba < 0.8)\n",
    "        low_conf_mask = max_proba < 0.6\n",
    "        \n",
    "        error_analysis['confidence_analysis'] = {\n",
    "            'high_confidence': {\n",
    "                'count': np.sum(high_conf_mask),\n",
    "                'errors': np.sum(~correct_predictions[high_conf_mask]),\n",
    "                'error_rate': np.mean(~correct_predictions[high_conf_mask]) if np.sum(high_conf_mask) > 0 else 0\n",
    "            },\n",
    "            'medium_confidence': {\n",
    "                'count': np.sum(med_conf_mask),\n",
    "                'errors': np.sum(~correct_predictions[med_conf_mask]),\n",
    "                'error_rate': np.mean(~correct_predictions[med_conf_mask]) if np.sum(med_conf_mask) > 0 else 0\n",
    "            },\n",
    "            'low_confidence': {\n",
    "                'count': np.sum(low_conf_mask),\n",
    "                'errors': np.sum(~correct_predictions[low_conf_mask]),\n",
    "                'error_rate': np.mean(~correct_predictions[low_conf_mask]) if np.sum(low_conf_mask) > 0 else 0\n",
    "            }\n",
    "        }\n",
    "    \n",
    "    # Per-class error analysis\n",
    "    unique_classes = np.unique(y_true)\n",
    "    error_analysis['per_class_errors'] = {}\n",
    "    \n",
    "    for class_label in unique_classes:\n",
    "        class_mask = y_true == class_label\n",
    "        class_correct = correct_predictions[class_mask]\n",
    "        \n",
    "        error_analysis['per_class_errors'][int(class_label)] = {\n",
    "            'total_samples': np.sum(class_mask),\n",
    "            'correct_predictions': np.sum(class_correct),\n",
    "            'errors': np.sum(~class_correct),\n",
    "            'error_rate': np.mean(~class_correct)\n",
    "        }\n",
    "    \n",
    "    # Confusion patterns\n",
    "    cm = confusion_matrix(y_true, y_pred)\n",
    "    error_analysis['confusion_patterns'] = []\n",
    "    \n",
    "    for i in range(len(unique_classes)):\n",
    "        for j in range(len(unique_classes)):\n",
    "            if i != j and cm[i, j] > 0:\n",
    "                error_analysis['confusion_patterns'].append({\n",
    "                    'true_class': int(unique_classes[i]),\n",
    "                    'predicted_class': int(unique_classes[j]),\n",
    "                    'count': int(cm[i, j]),\n",
    "                    'percentage': float(cm[i, j] / np.sum(cm[i, :]) * 100)\n",
    "                })\n",
    "    \n",
    "    # Sort confusion patterns by count\n",
    "    error_analysis['confusion_patterns'].sort(key=lambda x: x['count'], reverse=True)\n",
    "    \n",
    "    return error_analysis\n",
    "\n",
    "# Analyze errors for each model\n",
    "if rf_available:\n",
    "    print(f\"\\n🌲 RANDOM FOREST ERROR ANALYSIS:\")\n",
    "    rf_errors = analyze_errors(y_test_true, rf_y_test_pred, rf_y_test_proba, \"Random Forest\")\n",
    "    \n",
    "    print(f\"  • Total errors: {rf_errors['total_errors']} / {len(y_test_true)}\")\n",
    "    print(f\"  • Error rate: {rf_errors['error_rate']:.4f} ({rf_errors['error_rate']*100:.1f}%)\")\n",
    "    \n",
    "    if 'confidence_analysis' in rf_errors:\n",
    "        print(f\"  • High confidence errors: {rf_errors['confidence_analysis']['high_confidence']['errors']} / {rf_errors['confidence_analysis']['high_confidence']['count']} ({rf_errors['confidence_analysis']['high_confidence']['error_rate']*100:.1f}%)\")\n",
    "        print(f\"  • Low confidence errors: {rf_errors['confidence_analysis']['low_confidence']['errors']} / {rf_errors['confidence_analysis']['low_confidence']['count']} ({rf_errors['confidence_analysis']['low_confidence']['error_rate']*100:.1f}%)\")\n",
    "    \n",
    "    print(f\"  • Top confusion patterns:\")\n",
    "    for pattern in rf_errors['confusion_patterns'][:3]:\n",
    "        true_class_name = class_names[pattern['true_class']] if pattern['true_class'] < len(class_names) else f\"Class {pattern['true_class']}\"\n",
    "        pred_class_name = class_names[pattern['predicted_class']] if pattern['predicted_class'] < len(class_names) else f\"Class {pattern['predicted_class']}\"\n",
    "        print(f\"    - {true_class_name} → {pred_class_name}: {pattern['count']} cases ({pattern['percentage']:.1f}%)\")\n",
    "\n",
    "if xgb_available:\n",
    "    print(f\"\\n🚀 XGBOOST ERROR ANALYSIS:\")\n",
    "    xgb_errors = analyze_errors(y_test_true, xgb_y_test_pred, xgb_y_test_proba, \"XGBoost\")\n",
    "    \n",
    "    print(f\"  • Total errors: {xgb_errors['total_errors']} / {len(y_test_true)}\")\n",
    "    print(f\"  • Error rate: {xgb_errors['error_rate']:.4f} ({xgb_errors['error_rate']*100:.1f}%)\")\n",
    "    \n",
    "    if 'confidence_analysis' in xgb_errors:\n",
    "        print(f\"  • High confidence errors: {xgb_errors['confidence_analysis']['high_confidence']['errors']} / {xgb_errors['confidence_analysis']['high_confidence']['count']} ({xgb_errors['confidence_analysis']['high_confidence']['error_rate']*100:.1f}%)\")\n",
    "        print(f\"  • Low confidence errors: {xgb_errors['confidence_analysis']['low_confidence']['errors']} / {xgb_errors['confidence_analysis']['low_confidence']['count']} ({xgb_errors['confidence_analysis']['low_confidence']['error_rate']*100:.1f}%)\")\n",
    "    \n",
    "    print(f\"  • Top confusion patterns:\")\n",
    "    for pattern in xgb_errors['confusion_patterns'][:3]:\n",
    "        true_class_name = class_names[pattern['true_class']] if pattern['true_class'] < len(class_names) else f\"Class {pattern['true_class']}\"\n",
    "        pred_class_name = class_names[pattern['predicted_class']] if pattern['predicted_class'] < len(class_names) else f\"Class {pattern['predicted_class']}\"\n",
    "        print(f\"    - {true_class_name} → {pred_class_name}: {pattern['count']} cases ({pattern['percentage']:.1f}%)\")\n",
    "\n",
    "# Compare error patterns between models\n",
    "if both_models_available:\n",
    "    print(f\"\\n🔄 ERROR PATTERN COMPARISON:\")\n",
    "    \n",
    "    # Find cases where models disagree\n",
    "    disagreement_mask = rf_y_test_pred != xgb_y_test_pred\n",
    "    disagreement_count = np.sum(disagreement_mask)\n",
    "    \n",
    "    print(f\"  • Model disagreement: {disagreement_count} / {len(y_test_true)} ({disagreement_count/len(y_test_true)*100:.1f}%)\")\n",
    "    \n",
    "    # Analyze disagreement cases\n",
    "    if disagreement_count > 0:\n",
    "        rf_correct_disagree = np.sum((rf_y_test_pred == y_test_true) & disagreement_mask)\n",
    "        xgb_correct_disagree = np.sum((xgb_y_test_pred == y_test_true) & disagreement_mask)\n",
    "        both_wrong_disagree = disagreement_count - rf_correct_disagree - xgb_correct_disagree\n",
    "        \n",
    "        print(f\"  • RF correct, XGB wrong: {rf_correct_disagree} cases\")\n",
    "        print(f\"  • XGB correct, RF wrong: {xgb_correct_disagree} cases\")\n",
    "        print(f\"  • Both wrong: {both_wrong_disagree} cases\")\n",
    "        \n",
    "        # These are the hardest cases - both models get them wrong\n",
    "        if both_wrong_disagree > 0:\n",
    "            print(f\"  • Hardest cases (both wrong): {both_wrong_disagree} samples need attention\")\n",
    "\n",
    "print(f\"\\n✅ Error analysis completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📈 Performance Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create comprehensive performance visualizations\n",
    "print(\"📈 PERFORMANCE VISUALIZATION\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Prepare data for visualization\n",
    "models_to_plot = []\n",
    "if rf_available:\n",
    "    models_to_plot.append(('Random Forest', rf_eval, rf_y_test_pred, rf_y_test_proba))\n",
    "if xgb_available:\n",
    "    models_to_plot.append(('XGBoost', xgb_eval, xgb_y_test_pred, xgb_y_test_proba))\n",
    "\n",
    "# Add ensemble methods if available\n",
    "if ensemble_results:\n",
    "    if 'voting_soft' in ensemble_results:\n",
    "        models_to_plot.append(('Voting Soft', ensemble_results['voting_soft']['evaluation'], \n",
    "                              np.array(ensemble_results['voting_soft']['predictions']), \n",
    "                              np.array(ensemble_results['voting_soft']['probabilities'])))\n",
    "    if 'stacking' in ensemble_results:\n",
    "        models_to_plot.append(('Stacking', ensemble_results['stacking']['evaluation'], \n",
    "                              np.array(ensemble_results['stacking']['predictions']), \n",
    "                              np.array(ensemble_results['stacking']['probabilities'])))\n",
    "\n",
    "print(f\"\\n📊 Creating visualizations for {len(models_to_plot)} models...\")\n",
    "\n",
    "# 1. Performance Metrics Comparison\n",
    "fig = make_subplots(\n",
    "    rows=2, cols=2,\n",
    "    subplot_titles=('Performance Metrics Comparison', 'Confusion Matrix Heatmap',\n",
    "                   'ROC Curves (Binary)', 'Precision-Recall Curves'),\n",
    "    specs=[[{'type': 'bar'}, {'type': 'heatmap'}],\n",
    "           [{'type': 'scatter'}, {'type': 'scatter'}]]\n",
    ")\n",
    "\n",
    "# Performance metrics bar chart\n",
    "metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score', 'Kappa', 'MCC']\n",
    "colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink']\n",
    "\n",
    "for i, (model_name, evaluation, _, _) in enumerate(models_to_plot):\n",
    "    values = [evaluation['accuracy'], evaluation['precision'], evaluation['recall'], \n",
    "             evaluation['f1'], evaluation['kappa'], evaluation['mcc']]\n",
    "    \n",
    "    fig.add_trace(\n",
    "        go.Bar(x=metrics, y=values, name=model_name, \n",
    "               marker_color=colors[i % len(colors)]),\n",
    "        row=1, col=1\n",
    "    )\n",
    "\n",
    "# Confusion matrix for best model\n",
    "best_model_data = models_to_plot[0]  # Assume first is best, or find actual best\n",
    "if len(models_to_plot) > 1:\n",
    "    best_f1 = 0\n",
    "    for model_data in models_to_plot:\n",
    "        if model_data[1]['f1'] > best_f1:\n",
    "            best_f1 = model_data[1]['f1']\n",
    "            best_model_data = model_data\n",
    "\n",
    "best_cm = confusion_matrix(y_test_true, best_model_data[2])\n",
    "fig.add_trace(\n",
    "    go.Heatmap(z=best_cm, x=class_names, y=class_names, \n",
    "               colorscale='Blues', name=f'{best_model_data[0]} CM'),\n",
    "    row=1, col=2\n",
    ")\n",
    "\n",
    "# ROC curves (for binary classification)\n",
    "if len(class_names) == 2:\n",
    "    for model_name, evaluation, y_pred, y_proba in models_to_plot:\n",
    "        if y_proba is not None:\n",
    "            if len(y_proba.shape) > 1 and y_proba.shape[1] > 1:\n",
    "                y_proba_pos = y_proba[:, 1]\n",
    "            else:\n",
    "                y_proba_pos = y_proba\n",
    "            \n",
    "            fpr, tpr, _ = roc_curve(y_test_true, y_proba_pos)\n",
    "            auc_score = evaluation.get('roc_auc', 0)\n",
    "            \n",
    "            fig.add_trace(\n",
    "                go.Scatter(x=fpr, y=tpr, mode='lines',\n",
    "                          name=f'{model_name} (AUC={auc_score:.3f})'),\n",
    "                row=2, col=1\n",
    "            )\n",
    "    \n",
    "    # Add diagonal line\n",
    "    fig.add_trace(\n",
    "        go.Scatter(x=[0, 1], y=[0, 1], mode='lines',\n",
    "                  name='Random', line=dict(dash='dash', color='gray')),\n",
    "        row=2, col=1\n",
    "    )\n",
    "    \n",
    "    # Precision-Recall curves\n",
    "    for model_name, evaluation, y_pred, y_proba in models_to_plot:\n",
    "        if y_proba is not None:\n",
    "            if len(y_proba.shape) > 1 and y_proba.shape[1] > 1:\n",
    "                y_proba_pos = y_proba[:, 1]\n",
    "            else:\n",
    "                y_proba_pos = y_proba\n",
    "            \n",
    "            precision, recall, _ = precision_recall_curve(y_test_true, y_proba_pos)\n",
    "            avg_precision = evaluation.get('avg_precision', 0)\n",
    "            \n",
    "            fig.add_trace(\n",
    "                go.Scatter(x=recall, y=precision, mode='lines',\n",
    "                          name=f'{model_name} (AP={avg_precision:.3f})'),\n",
    "                row=2, col=2\n",
    "            )\n",
    "\n",
    "# Update layout\n",
    "fig.update_layout(height=800, showlegend=True, \n",
    "                 title_text=\"📊 Model Evaluation Dashboard\")\n",
    "\n",
    "# Update axes labels\n",
    "fig.update_xaxes(title_text=\"False Positive Rate\", row=2, col=1)\n",
    "fig.update_yaxes(title_text=\"True Positive Rate\", row=2, col=1)\n",
    "fig.update_xaxes(title_text=\"Recall\", row=2, col=2)\n",
    "fig.update_yaxes(title_text=\"Precision\", row=2, col=2)\n",
    "\n",
    "fig.show()\n",
    "\n",
    "# 2. Error Analysis Visualization\n",
    "if both_models_available:\n",
    "    print(f\"\\n📊 Creating error analysis visualization...\")\n",
    "    \n",
    "    # Error comparison chart\n",
    "    fig_errors = make_subplots(\n",
    "        rows=1, cols=2,\n",
    "        subplot_titles=('Error Rates by Confidence', 'Confusion Pattern Comparison'),\n",
    "        specs=[[{'type': 'bar'}, {'type': 'bar'}]]\n",
    "    )\n",
    "    \n",
    "    # Error rates by confidence level\n",
    "    confidence_levels = ['High (≥0.8)', 'Medium (0.6-0.8)', 'Low (<0.6)']\n",
    "    \n",
    "    if rf_available and 'confidence_analysis' in rf_errors:\n",
    "        rf_error_rates = [\n",
    "            rf_errors['confidence_analysis']['high_confidence']['error_rate'] * 100,\n",
    "            rf_errors['confidence_analysis']['medium_confidence']['error_rate'] * 100,\n",
    "            rf_errors['confidence_analysis']['low_confidence']['error_rate'] * 100\n",
    "        ]\n",
    "        \n",
    "        fig_errors.add_trace(\n",
    "            go.Bar(x=confidence_levels, y=rf_error_rates, name='Random Forest',\n",
    "                  marker_color='lightblue'),\n",
    "            row=1, col=1\n",
    "        )\n",
    "    \n",
    "    if xgb_available and 'confidence_analysis' in xgb_errors:\n",
    "        xgb_error_rates = [\n",
    "            xgb_errors['confidence_analysis']['high_confidence']['error_rate'] * 100,\n",
    "            xgb_errors['confidence_analysis']['medium_confidence']['error_rate'] * 100,\n",
    "            xgb_errors['confidence_analysis']['low_confidence']['error_rate'] * 100\n",
    "        ]\n",
    "        \n",
    "        fig_errors.add_trace(\n",
    "            go.Bar(x=confidence_levels, y=xgb_error_rates, name='XGBoost',\n",
    "                  marker_color='lightgreen'),\n",
    "            row=1, col=1\n",
    "        )\n",
    "    \n",
    "    # Top confusion patterns\n",
    "    if rf_available and xgb_available:\n",
    "        rf_top_patterns = [f\"{class_names[p['true_class']] if p['true_class'] < len(class_names) else f'C{p[\"true_class\"]}'}→{class_names[p['predicted_class']] if p['predicted_class'] < len(class_names) else f'C{p[\"predicted_class\"]}'}\" \n",
    "                          for p in rf_errors['confusion_patterns'][:5]]\n",
    "        rf_pattern_counts = [p['count'] for p in rf_errors['confusion_patterns'][:5]]\n",
    "        \n",
    "        xgb_top_patterns = [f\"{class_names[p['true_class']] if p['true_class'] < len(class_names) else f'C{p[\"true_class\"]}'}→{class_names[p['predicted_class']] if p['predicted_class'] < len(class_names) else f'C{p[\"predicted_class\"]}'}\" \n",
    "                           for p in xgb_errors['confusion_patterns'][:5]]\n",
    "        xgb_pattern_counts = [p['count'] for p in xgb_errors['confusion_patterns'][:5]]\n",
    "        \n",
    "        # Use common patterns for comparison\n",
    "        all_patterns = list(set(rf_top_patterns + xgb_top_patterns))\n",
    "        \n",
    "        rf_counts = []\n",
    "        xgb_counts = []\n",
    "        \n",
    "        for pattern in all_patterns:\n",
    "            rf_count = rf_pattern_counts[rf_top_patterns.index(pattern)] if pattern in rf_top_patterns else 0\n",
    "            xgb_count = xgb_pattern_counts[xgb_top_patterns.index(pattern)] if pattern in xgb_top_patterns else 0\n",
    "            rf_counts.append(rf_count)\n",
    "            xgb_counts.append(xgb_count)\n",
    "        \n",
    "        fig_errors.add_trace(\n",
    "            go.Bar(x=all_patterns, y=rf_counts, name='RF Errors',\n",
    "                  marker_color='lightcoral'),\n",
    "            row=1, col=2\n",
    "        )\n",
    "        \n",
    "        fig_errors.add_trace(\n",
    "            go.Bar(x=all_patterns, y=xgb_counts, name='XGB Errors',\n",
    "                  marker_color='lightyellow'),\n",
    "            row=1, col=2\n",
    "        )\n",
    "    \n",
    "    fig_errors.update_layout(height=400, showlegend=True,\n",
    "                            title_text=\"🔍 Error Analysis Dashboard\")\n",
    "    fig_errors.update_yaxes(title_text=\"Error Rate (%)\", row=1, col=1)\n",
    "    fig_errors.update_yaxes(title_text=\"Error Count\", row=1, col=2)\n",
    "    fig_errors.update_xaxes(title_text=\"Confusion Patterns\", row=1, col=2)\n",
    "    \n",
    "    fig_errors.show()\n",
    "\n",
    "print(f\"\\n✅ Performance visualization completed!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 💾 Save Final Results"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Save comprehensive evaluation results\n",
    "print(\"💾 SAVING FINAL RESULTS\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "# Create directories\n",
    "os.makedirs('models/final', exist_ok=True)\n",
    "os.makedirs('data/evaluation', exist_ok=True)\n",
    "os.makedirs('data/final_results', exist_ok=True)\n",
    "\n",
    "# Determine final best model\n",
    "final_model_info = {\n",
    "    'evaluation_date': datetime.now().isoformat(),\n",
    "    'models_evaluated': [],\n",
    "    'best_model': None,\n",
    "    'best_performance': None\n",
    "}\n",
    "\n",
    "# Collect all model performances\n",
    "all_model_performances = []\n",
    "\n",
    "if rf_available:\n",
    "    all_model_performances.append({\n",
    "        'name': 'Random Forest',\n",
    "        'type': 'individual',\n",
    "        'evaluation': rf_eval,\n",
    "        'model_path': 'models/random_forest/rf_production_artifacts.pkl'\n",
    "    })\n",
    "    final_model_info['models_evaluated'].append('Random Forest')\n",
    "\n",
    "if xgb_available:\n",
    "    all_model_performances.append({\n",
    "        'name': 'XGBoost',\n",
    "        'type': 'individual',\n",
    "        'evaluation': xgb_eval,\n",
    "        'model_path': 'models/xgboost/xgb_production_artifacts.pkl'\n",
    "    })\n",
    "    final_model_info['models_evaluated'].append('XGBoost')\n",
    "\n",
    "# Add ensemble methods\n",
    "if ensemble_results:\n",
    "    for ensemble_name, ensemble_data in ensemble_results.items():\n",
    "        if 'evaluation' in ensemble_data:\n",
    "            all_model_performances.append({\n",
    "                'name': ensemble_name.replace('_', ' ').title(),\n",
    "                'type': 'ensemble',\n",
    "                'evaluation': ensemble_data['evaluation'],\n",
    "                'model_path': None  # Ensemble models handled separately\n",
    "            })\n",
    "            final_model_info['models_evaluated'].append(ensemble_name)\n",
    "\n",
    "# Find best model based on F1 score\n",
    "best_performance = max(all_model_performances, key=lambda x: x['evaluation']['f1'])\n",
    "final_model_info['best_model'] = best_performance['name']\n",
    "final_model_info['best_performance'] = best_performance['evaluation']\n",
    "\n",
    "print(f\"\\n🏆 FINAL MODEL SELECTION:\")\n",
    "print(f\"  • Best model: {final_model_info['best_model']}\")\n",
    "print(f\"  • Best F1-score: {final_model_info['best_performance']['f1']:.4f}\")\n",
    "print(f\"  • Best accuracy: {final_model_info['best_performance']['accuracy']:.4f}\")\n",
    "\n",
    "# Save best model to final directory\n",
    "if best_performance['model_path'] and os.path.exists(best_performance['model_path']):\n",
    "    import shutil\n",
    "    final_model_path = 'models/final/best_sentiment_model.pkl'\n",
    "    shutil.copy2(best_performance['model_path'], final_model_path)\n",
    "    print(f\"✅ Best model copied to: {final_model_path}\")\n",
    "elif ensemble_results and best_performance['type'] == 'ensemble':\n",
    "    # Save ensemble model\n",
    "    ensemble_name = best_performance['name'].lower().replace(' ', '_')\n",
    "    if ensemble_name in ensemble_results and 'model' in ensemble_results[ensemble_name]:\n",
    "        final_model_path = 'models/final/best_sentiment_model.pkl'\n",
    "        joblib.dump(ensemble_results[ensemble_name]['model'], final_model_path)\n",
    "        print(f\"✅ Best ensemble model saved to: {final_model_path}\")\n",
    "\n",
    "# Save comprehensive evaluation results\n",
    "evaluation_results = {\n",
    "    'evaluation_summary': final_model_info,\n",
    "    'individual_models': {},\n",
    "    'ensemble_results': ensemble_results if ensemble_results else {},\n",
    "    'error_analysis': {},\n",
    "    'statistical_tests': {},\n",
    "    'dataset_info': {\n",
    "        'test_samples': len(y_test_true),\n",
    "        'classes': class_names,\n",
    "        'class_distribution': dict(zip(*np.unique(y_test_true, return_counts=True))),\n",
    "        'labeling_method': labeling_method\n",
    "    }\n",
    "}\n",
    "\n",
    "# Add individual model results\n",
    "if rf_available:\n",
    "    evaluation_results['individual_models']['random_forest'] = {\n",
    "        'evaluation': rf_eval,\n",
    "        'error_analysis': rf_errors if 'rf_errors' in locals() else None\n",
    "    }\n",
    "\n",
    "if xgb_available:\n",
    "    evaluation_results['individual_models']['xgboost'] = {\n",
    "        'evaluation': xgb_eval,\n",
    "        'error_analysis': xgb_errors if 'xgb_errors' in locals() else None\n",
    "    }\n",
    "\n",
    "# Add statistical tests\n",
    "if both_models_available:\n",
    "    evaluation_results['statistical_tests'] = {\n",
    "        'mcnemar_test': {\n",
    "            'statistic': mcnemar_stat if 'mcnemar_stat' in locals() else None,\n",
    "            'p_value': mcnemar_p if 'mcnemar_p' in locals() else None\n",
    "        },\n",
    "        'model_agreement': agreement if 'agreement' in locals() else None,\n",
    "        'disagreement_analysis': {\n",
    "            'total_disagreements': disagreement_count if 'disagreement_count' in locals() else None,\n",
    "            'rf_correct_xgb_wrong': rf_correct_disagree if 'rf_correct_disagree' in locals() else None,\n",
    "            'xgb_correct_rf_wrong': xgb_correct_disagree if 'xgb_correct_disagree' in locals() else None\n",
    "        }\n",
    "    }\n",
    "\n",
    "# Save evaluation results\n",
    "evaluation_file = 'data/evaluation/comprehensive_evaluation.json'\n",
    "with open(evaluation_file, 'w') as f:\n",
    "    json.dump(evaluation_results, f, indent=2, default=str)\n",
    "print(f\"✅ Comprehensive evaluation saved: {evaluation_file}\")\n",
    "\n",
    "# Save model comparison table\n",
    "comparison_data = []\n",
    "for model_perf in all_model_performances:\n",
    "    eval_data = model_perf['evaluation']\n",
    "    comparison_data.append({\n",
    "        'Model': model_perf['name'],\n",
    "        'Type': model_perf['type'],\n",
    "        'Accuracy': eval_data['accuracy'],\n",
    "        'Precision': eval_data['precision'],\n",
    "        'Recall': eval_data['recall'],\n",
    "        'F1_Score': eval_data['f1'],\n",
    "        'Kappa': eval_data['kappa'],\n",
    "        'MCC': eval_data['mcc'],\n",
    "        'ROC_AUC': eval_data.get('roc_auc', None),\n",
    "        'Log_Loss': eval_data.get('log_loss', None)\n",
    "    })\n",
    "\n",
    "comparison_df = pd.DataFrame(comparison_data)\n",
    "comparison_file = 'data/evaluation/model_comparison.xlsx'\n",
    "comparison_df.to_excel(comparison_file, index=False)\n",
    "print(f\"✅ Model comparison table saved: {comparison_file}\")\n",
    "\n",
    "# Create final production utility\n",
    "final_utils_code = f'''\n",
    "import joblib\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "from datetime import datetime\n",
    "\n",
    "# Best model information\n",
    "BEST_MODEL_NAME = \"{final_model_info['best_model']}\"\n",
    "BEST_MODEL_F1 = {final_model_info['best_performance']['f1']:.4f}\n",
    "BEST_MODEL_ACCURACY = {final_model_info['best_performance']['accuracy']:.4f}\n",
    "EVALUATION_DATE = \"{final_model_info['evaluation_date']}\"\n",
    "\n",
    "def load_best_model(model_path='models/final/best_sentiment_model.pkl'):\n",
    "    \"\"\"Load the best performing sentiment analysis model\"\"\"\n",
    "    return joblib.load(model_path)\n",
    "\n",
    "def predict_sentiment(texts, model_path='models/final/best_sentiment_model.pkl'):\n",
    "    \"\"\"Predict sentiment using the best model\"\"\"\n",
    "    # This is a placeholder - actual implementation depends on best model type\n",
    "    if BEST_MODEL_NAME == \"Random Forest\":\n",
    "        from utils.random_forest_utils import predict_sentiment_rf\n",
    "        return predict_sentiment_rf(texts, 'models/random_forest/rf_production_artifacts.pkl')\n",
    "    elif BEST_MODEL_NAME == \"XGBoost\":\n",
    "        from utils.xgboost_utils import predict_sentiment_xgb\n",
    "        return predict_sentiment_xgb(texts, 'models/xgboost/xgb_production_artifacts.pkl')\n",
    "    else:\n",
    "        # For ensemble methods, load the saved model\n",
    "        model = load_best_model(model_path)\n",
    "        # Implementation would depend on ensemble type\n",
    "        raise NotImplementedError(f\"Prediction for {{BEST_MODEL_NAME}} not implemented\")\n",
    "\n",
    "def get_model_info():\n",
    "    \"\"\"Get information about the best model\"\"\"\n",
    "    return {{\n",
    "        'name': BEST_MODEL_NAME,\n",
    "        'f1_score': BEST_MODEL_F1,\n",
    "        'accuracy': BEST_MODEL_ACCURACY,\n",
    "        'evaluation_date': EVALUATION_DATE\n",
    "    }}\n",
    "\n",
    "def load_evaluation_results(file_path='data/evaluation/comprehensive_evaluation.json'):\n",
    "    \"\"\"Load comprehensive evaluation results\"\"\"\n",
    "    import json\n",
    "    with open(file_path, 'r') as f:\n",
    "        return json.load(f)\n",
    "'''\n",
    "\n",
    "with open('utils/final_model_utils.py', 'w', encoding='utf-8') as f:\n",
    "    f.write(final_utils_code)\n",
    "\n",
    "print(f\"\\n🔧 Final model utilities saved: utils/final_model_utils.py\")\n",
    "\n",
    "print(f\"\\n📊 FILES GENERATED SUMMARY:\")\n",
    "print(f\"  📁 models/final/\")\n",
    "print(f\"     • best_sentiment_model.pkl - Best performing model\")\n",
    "print(f\"  📁 data/evaluation/\")\n",
    "print(f\"     • comprehensive_evaluation.json - Complete evaluation results\")\n",
    "print(f\"     • model_comparison.xlsx - Model comparison table\")\n",
    "print(f\"  🔧 utils/final_model_utils.py - Production utilities\")\n",
    "\n",
    "print(f\"\\n✅ Final results saved successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📋 Summary & Recommendations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Generate comprehensive summary and recommendations\n",
    "print(\"📋 MODEL EVALUATION SUMMARY & RECOMMENDATIONS\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "print(f\"\\n🎯 EVALUATION COMPLETED:\")\n",
    "print(f\"  • Models evaluated: {len(final_model_info['models_evaluated'])}\")\n",
    "print(f\"  • Test samples: {len(y_test_true)}\")\n",
    "print(f\"  • Classes: {class_names}\")\n",
    "print(f\"  • Evaluation date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n",
    "\n",
    "print(f\"\\n🏆 BEST MODEL SELECTION:\")\n",
    "print(f\"  • Winner: {final_model_info['best_model']}\")\n",
    "print(f\"  • Test Accuracy: {final_model_info['best_performance']['accuracy']:.4f}\")\n",
    "print(f\"  • Test F1-Score: {final_model_info['best_performance']['f1']:.4f}\")\n",
    "print(f\"  • Test Precision: {final_model_info['best_performance']['precision']:.4f}\")\n",
    "print(f\"  • Test Recall: {final_model_info['best_performance']['recall']:.4f}\")\n",
    "print(f\"  • Cohen's Kappa: {final_model_info['best_performance']['kappa']:.4f}\")\n",
    "print(f\"  • Matthews Correlation: {final_model_info['best_performance']['mcc']:.4f}\")\n",
    "\n",
    "if 'roc_auc' in final_model_info['best_performance'] and final_model_info['best_performance']['roc_auc']:\n",
    "    print(f\"  • ROC AUC: {final_model_info['best_performance']['roc_auc']:.4f}\")\n",
    "\n",
    "print(f\"\\n📊 MODEL COMPARISON SUMMARY:\")\n",
    "print(f\"{'Model':<20} {'Accuracy':<10} {'F1-Score':<10} {'Type':<12}\")\n",
    "print(\"-\" * 55)\n",
    "\n",
    "for model_perf in sorted(all_model_performances, key=lambda x: x['evaluation']['f1'], reverse=True):\n",
    "    name = model_perf['name']\n",
    "    acc = model_perf['evaluation']['accuracy']\n",
    "    f1 = model_perf['evaluation']['f1']\n",
    "    model_type = model_perf['type']\n",
    "    \n",
    "    marker = \"🏆\" if name == final_model_info['best_model'] else \"  \"\n",
    "    print(f\"{marker} {name:<18} {acc:<10.4f} {f1:<10.4f} {model_type:<12}\")\n",
    "\n",
    "if both_models_available:\n",
    "    print(f\"\\n📈 STATISTICAL ANALYSIS:\")\n",
    "    if 'mcnemar_p' in locals():\n",
    "        significance = \"Significant\" if mcnemar_p < 0.05 else \"Not significant\"\n",
    "        print(f\"  • McNemar's test: p = {mcnemar_p:.4f} ({significance})\")\n",
    "    \n",
    "    if 'agreement' in locals():\n",
    "        print(f\"  • Model agreement: {agreement:.4f} ({agreement*100:.1f}%)\")\n",
    "    \n",
    "    if 'disagreement_count' in locals():\n",
    "        print(f\"  • Disagreement cases: {disagreement_count} ({disagreement_count/len(y_test_true)*100:.1f}%)\")\n",
    "\n",
    "print(f\"\\n🔍 ERROR ANALYSIS INSIGHTS:\")\n",
    "if rf_available and 'rf_errors' in locals():\n",
    "    print(f\"  • Random Forest error rate: {rf_errors['error_rate']*100:.1f}%\")\n",
    "    if 'confidence_analysis' in rf_errors:\n",
    "        high_conf_errors = rf_errors['confidence_analysis']['high_confidence']['error_rate']\n",
    "        print(f\"    - High confidence errors: {high_conf_errors*100:.1f}%\")\n",
    "\n",
    "if xgb_available and 'xgb_errors' in locals():\n",
    "    print(f\"  • XGBoost error rate: {xgb_errors['error_rate']*100:.1f}%\")\n",
    "    if 'confidence_analysis' in xgb_errors:\n",
    "        high_conf_errors = xgb_errors['confidence_analysis']['high_confidence']['error_rate']\n",
    "        print(f\"    - High confidence errors: {high_conf_errors*100:.1f}%\")\n",
    "\n",
    "print(f\"\\n🎯 RECOMMENDATIONS:\")\n",
    "\n",
    "# Performance-based recommendations\n",
    "best_f1 = final_model_info['best_performance']['f1']\n",
    "if best_f1 >= 0.9:\n",
    "    print(f\"  ✅ Excellent performance (F1 ≥ 0.9): Ready for production deployment\")\n",
    "elif best_f1 >= 0.8:\n",
    "    print(f\"  ✅ Good performance (F1 ≥ 0.8): Suitable for production with monitoring\")\n",
    "elif best_f1 >= 0.7:\n",
    "    print(f\"  ⚠️ Moderate performance (F1 ≥ 0.7): Consider improvements before production\")\n",
    "else:\n",
    "    print(f\"  ❌ Poor performance (F1 < 0.7): Significant improvements needed\")\n",
    "\n",
    "# Model-specific recommendations\n",
    "if final_model_info['best_model'] in ['Random Forest', 'XGBoost']:\n",
    "    print(f\"  • Individual model selected: Consider ensemble methods for potential improvement\")\n",
    "elif 'ensemble' in final_model_info['best_model'].lower():\n",
    "    print(f\"  • Ensemble method selected: Good choice for robust predictions\")\n",
    "\n",
    "# Error analysis recommendations\n",
    "if both_models_available and 'disagreement_count' in locals():\n",
    "    if disagreement_count > len(y_test_true) * 0.1:  # More than 10% disagreement\n",
    "        print(f\"  • High model disagreement: Review conflicting cases for data quality\")\n",
    "    \n",
    "    if 'both_wrong_disagree' in locals() and both_wrong_disagree > 0:\n",
    "        print(f\"  • {both_wrong_disagree} hard cases identified: Consider manual review or additional features\")\n",
    "\n",
    "# Confidence-based recommendations\n",
    "if 'rf_errors' in locals() and 'confidence_analysis' in rf_errors:\n",
    "    low_conf_count = rf_errors['confidence_analysis']['low_confidence']['count']\n",
    "    if low_conf_count > len(y_test_true) * 0.2:  # More than 20% low confidence\n",
    "        print(f\"  • High proportion of low-confidence predictions: Consider confidence thresholding\")\n",
    "\n",
    "print(f\"\\n🚀 PRODUCTION READINESS:\")\n",
    "print(f\"  • Best model saved: models/final/best_sentiment_model.pkl\")\n",
    "print(f\"  • Production utilities: utils/final_model_utils.py\")\n",
    "print(f\"  • Evaluation results: data/evaluation/comprehensive_evaluation.json\")\n",
    "print(f\"  • Model comparison: data/evaluation/model_comparison.xlsx\")\n",
    "\n",
    "print(f\"\\n📋 NEXT STEPS:\")\n",
    "print(f\"  1. Deploy best model to production environment\")\n",
    "print(f\"  2. Implement monitoring for prediction confidence and drift\")\n",
    "print(f\"  3. Set up feedback loop for continuous improvement\")\n",
    "print(f\"  4. Consider A/B testing with different models\")\n",
    "print(f\"  5. Regular retraining schedule based on new data\")\n",
    "\n",
    "if best_f1 < 0.8:\n",
    "    print(f\"\\n⚠️ IMPROVEMENT SUGGESTIONS:\")\n",
    "    print(f\"  • Collect more training data, especially for underperforming classes\")\n",
    "    print(f\"  • Feature engineering: add domain-specific features\")\n",
    "    print(f\"  • Hyperparameter tuning: more extensive search\")\n",
    "    print(f\"  • Data quality: review and clean training data\")\n",
    "    print(f\"  • Advanced models: consider transformer-based models\")\n",
    "\n",
    "print(f\"\\n✅ MODEL EVALUATION COMPLETED SUCCESSFULLY!\")\n",
    "print(f\"📅 Evaluation completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n",
    "print(f\"\\n🎉 Sentiment analysis model is ready for deployment!\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n",
   "language": "python",\n",
   "name": "python3"\n",
  },\n",
  "language_info": {\n",
   "codemirror_mode": {\n",
    "name": "ipython",\n",
    "version": 3\n",
   },\n",
   "file_extension": ".py",\n",
   "mimetype": "text/x-python",\n",
   "name": "python",\n",
   "nbconvert_exporter": "python",\n",
   "pygments_lexer": "ipython3",\n",
   "version": "3.8.0"\n",
  }\n },\n "nbformat": 4,\n "nbformat_minor": 4\n}
