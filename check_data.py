import pandas as pd
import sys
import os

# Check if file exists
file_path = 'reviews_gofood_Merchant.xlsx'
if not os.path.exists(file_path):
    file_path = '../reviews_gofood_Merchant.xlsx'

if os.path.exists(file_path):
    print(f"✅ Found data file: {file_path}")
    
    try:
        # Load the Excel file
        df = pd.read_excel(file_path)
        
        print(f"\n📊 Dataset Info:")
        print(f"Shape: {df.shape}")
        print(f"Columns: {df.columns.tolist()}")
        
        print(f"\n📋 First 3 rows:")
        print(df.head(3).to_string())
        
        print(f"\n🔍 Column Details:")
        for col in df.columns:
            print(f"- {col}: {df[col].dtype} (non-null: {df[col].count()})")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")
else:
    print(f"❌ Data file not found: {file_path}")
