"""
Comprehensive Analysis Page for GoFood Sentiment Analysis
Following the action plan for step-by-step analysis and preprocessing
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import APP_CONFIG
from components.styling import load_css, create_header, create_info_card
from utils.data_loader import load_raw_data, load_preprocessed_data
from utils.comprehensive_analysis import (
    analyze_raw_data,
    comprehensive_text_preprocessing,
    preprocess_dataset_comprehensive,
    analyze_preprocessing_impact,
    extract_features_comprehensive
)

def main():
    """Comprehensive Analysis main function"""
    
    # Configure page
    st.set_page_config(
        page_title="Comprehensive Analysis - GoFood Sentiment Analysis",
        page_icon="🔬",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="🔬 Analisis Komprehensif",
        subtitle="Analisis Step-by-Step: Data Mentah → Preprocessing → Feature Extraction"
    )
    
    # Load data
    with st.spinner("🔄 Loading data..."):
        df_raw = load_raw_data()
    
    if df_raw is None:
        st.error("❌ Failed to load data")
        st.stop()
    
    # Create tabs for different analysis phases
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 Phase 1: Raw Data Analysis", 
        "🔄 Phase 2: Text Preprocessing", 
        "📈 Phase 3: Preprocessing Impact",
        "🔍 Phase 4: Feature Extraction",
        "📋 Phase 5: Summary & Insights"
    ])
    
    with tab1:
        show_raw_data_analysis(df_raw)
    
    with tab2:
        show_text_preprocessing(df_raw)
    
    with tab3:
        show_preprocessing_impact(df_raw)
    
    with tab4:
        show_feature_extraction(df_raw)
    
    with tab5:
        show_summary_insights(df_raw)

def show_raw_data_analysis(df):
    """Phase 1: Analyze raw data before any preprocessing"""
    
    st.header("📊 Phase 1: Analisis Data Mentah")
    st.markdown("**Tujuan**: Memahami karakteristik data sebelum preprocessing")
    
    # Perform raw data analysis
    with st.spinner("🔍 Analyzing raw data..."):
        analysis = analyze_raw_data(df)
    
    # Display basic statistics
    st.subheader("📋 Statistik Dasar")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Records", f"{analysis['basic_stats']['total_records']:,}")
    
    with col2:
        st.metric("Total Columns", analysis['basic_stats']['total_columns'])
    
    with col3:
        st.metric("Memory Usage", f"{analysis['basic_stats']['memory_usage']:.2f} MB")
    
    with col4:
        if analysis['data_quality']:
            st.metric("Data Quality", f"{analysis['data_quality']['completeness_score']:.1f}%")
    
    # Text analysis
    if analysis['text_stats']:
        st.subheader("📝 Analisis Teks (Kolom 'ulasan')")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Statistik Panjang Teks:**")
            text_stats = analysis['text_stats']
            
            stats_data = {
                'Metric': ['Rata-rata Karakter', 'Min Karakter', 'Max Karakter', 'Median Karakter',
                          'Rata-rata Kata', 'Total Kata', 'Teks Kosong'],
                'Value': [
                    f"{text_stats['avg_length']:.1f}",
                    f"{text_stats['min_length']}",
                    f"{text_stats['max_length']}",
                    f"{text_stats['median_length']:.1f}",
                    f"{text_stats['avg_words_per_text']:.1f}",
                    f"{text_stats['total_words']:,}",
                    f"{text_stats['empty_texts']}"
                ]
            }
            
            st.dataframe(pd.DataFrame(stats_data), use_container_width=True, hide_index=True)
        
        with col2:
            st.markdown("**Distribusi Panjang Teks:**")
            
            # Create histogram of text lengths
            text_lengths = df['ulasan'].astype(str).str.len()
            
            fig = px.histogram(
                x=text_lengths,
                nbins=50,
                title="Distribusi Panjang Teks (Karakter)",
                labels={'x': 'Panjang Karakter', 'y': 'Frekuensi'}
            )
            
            st.plotly_chart(fig, use_container_width=True)
        
        # Most common words
        st.subheader("🔤 Kata-kata Paling Sering Muncul (Top 20)")
        
        if 'most_common_words' in text_stats:
            words_data = pd.DataFrame(
                text_stats['most_common_words'], 
                columns=['Kata', 'Frekuensi']
            )
            
            col1, col2 = st.columns([1, 2])
            
            with col1:
                st.dataframe(words_data, use_container_width=True, hide_index=True)
            
            with col2:
                fig = px.bar(
                    words_data.head(15), 
                    x='Frekuensi', 
                    y='Kata',
                    orientation='h',
                    title="Top 15 Kata Paling Sering"
                )
                fig.update_layout(yaxis={'categoryorder': 'total ascending'})
                st.plotly_chart(fig, use_container_width=True)
    
    # Rating analysis
    if analysis['rating_analysis']:
        st.subheader("⭐ Analisis Rating (Kolom 'nilai')")
        
        col1, col2 = st.columns(2)
        
        with col1:
            rating_stats = analysis['rating_analysis']
            
            rating_metrics = {
                'Metric': ['Rata-rata Rating', 'Median Rating', 'Std Deviasi', 'Min Rating', 'Max Rating'],
                'Value': [
                    f"{rating_stats['avg_rating']:.2f}",
                    f"{rating_stats['median_rating']:.1f}",
                    f"{rating_stats['rating_std']:.2f}",
                    f"{rating_stats['min_rating']}",
                    f"{rating_stats['max_rating']}"
                ]
            }
            
            st.dataframe(pd.DataFrame(rating_metrics), use_container_width=True, hide_index=True)
        
        with col2:
            # Rating distribution chart
            rating_dist = pd.DataFrame(
                list(rating_stats['rating_distribution'].items()),
                columns=['Rating', 'Count']
            ).sort_values('Rating')
            
            fig = px.bar(
                rating_dist,
                x='Rating',
                y='Count',
                title="Distribusi Rating",
                labels={'Count': 'Jumlah Review'}
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    # Data quality assessment
    if analysis['data_quality']:
        st.subheader("🔍 Penilaian Kualitas Data")
        
        quality = analysis['data_quality']
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Missing Values", f"{quality['total_missing']}")
            st.metric("Missing %", f"{quality['missing_percentage']:.2f}%")
        
        with col2:
            st.metric("Duplicate Rows", f"{quality['duplicate_rows']}")
            st.metric("Duplicate %", f"{quality['duplicate_percentage']:.2f}%")
        
        with col3:
            st.metric("Completeness Score", f"{quality['completeness_score']:.1f}%")
        
        # Missing values by column
        if quality['total_missing'] > 0:
            missing_data = pd.DataFrame([
                {'Column': k, 'Missing Count': v, 'Missing %': f"{(v/len(df)*100):.2f}%"}
                for k, v in quality['missing_values'].items() if v > 0
            ])
            
            st.markdown("**Missing Values by Column:**")
            st.dataframe(missing_data, use_container_width=True, hide_index=True)

def show_text_preprocessing(df):
    """Phase 2: Show text preprocessing steps"""
    
    st.header("🔄 Phase 2: Text Preprocessing")
    st.markdown("**Tujuan**: Membersihkan dan memproses teks untuk analisis yang lebih baik")
    
    # Show preprocessing steps for sample text
    st.subheader("🔍 Demo Preprocessing Steps")
    
    # Select sample text
    sample_options = df['ulasan'].dropna().head(10).tolist()
    selected_text = st.selectbox(
        "Pilih contoh teks untuk melihat proses preprocessing:",
        options=sample_options,
        format_func=lambda x: x[:100] + "..." if len(x) > 100 else x
    )
    
    if selected_text:
        # Show preprocessing steps
        from utils.comprehensive_analysis import comprehensive_text_preprocessing
        
        steps = comprehensive_text_preprocessing(selected_text)
        
        st.markdown("**Langkah-langkah Preprocessing:**")
        
        for i, (step_name, step_text) in enumerate(steps.items(), 1):
            with st.expander(f"Step {i}: {step_name.replace('_', ' ').title()}", expanded=False):
                st.text_area(
                    f"Result of {step_name}:",
                    value=step_text,
                    height=100,
                    key=f"step_{i}_{step_name}"
                )
                
                # Show statistics for this step
                if step_text:
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Characters", len(step_text))
                    with col2:
                        st.metric("Words", len(step_text.split()))
                    with col3:
                        if step_name != 'original':
                            reduction = ((len(steps['original']) - len(step_text)) / len(steps['original'])) * 100
                            st.metric("Reduction", f"{reduction:.1f}%")
    
    # Process entire dataset
    st.subheader("🔄 Process Entire Dataset")
    
    if st.button("🚀 Start Comprehensive Preprocessing", type="primary"):
        
        with st.spinner("🔄 Processing entire dataset... This may take a few minutes."):
            
            # Store processed data in session state
            if 'df_processed' not in st.session_state:
                st.session_state.df_processed = preprocess_dataset_comprehensive(df, 'ulasan')
            
            df_processed = st.session_state.df_processed
            
            st.success("✅ Preprocessing completed!")
            
            # Show sample of processed data
            st.subheader("📋 Sample Processed Data")
            
            # Select columns to show
            preprocessing_columns = [col for col in df_processed.columns if 'ulasan_' in col]
            
            sample_df = df_processed[['ulasan'] + preprocessing_columns].head(5)
            
            for i, row in sample_df.iterrows():
                with st.expander(f"Sample {i+1}: {row['ulasan'][:100]}...", expanded=False):
                    for col in preprocessing_columns:
                        st.text_area(
                            f"{col.replace('ulasan_', '').replace('_', ' ').title()}:",
                            value=row[col],
                            height=60,
                            key=f"sample_{i}_{col}"
                        )

def show_preprocessing_impact(df):
    """Phase 3: Show impact of preprocessing"""
    
    st.header("📈 Phase 3: Analisis Dampak Preprocessing")
    st.markdown("**Tujuan**: Menganalisis perubahan yang terjadi setelah preprocessing")
    
    if 'df_processed' not in st.session_state:
        st.warning("⚠️ Please run preprocessing in Phase 2 first")
        return
    
    df_processed = st.session_state.df_processed
    
    # Analyze preprocessing impact
    with st.spinner("📊 Analyzing preprocessing impact..."):
        impact = analyze_preprocessing_impact(df, df_processed, 'ulasan')
    
    # Length comparison
    st.subheader("📏 Perbandingan Panjang Teks")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Rata-rata Karakter (Original)",
            f"{impact['length_comparison']['original_avg_length']:.1f}"
        )
    
    with col2:
        st.metric(
            "Rata-rata Karakter (Processed)",
            f"{impact['length_comparison']['processed_avg_length']:.1f}"
        )
    
    with col3:
        st.metric(
            "Pengurangan Panjang",
            f"{impact['length_comparison']['length_reduction_pct']:.1f}%"
        )
    
    # Word comparison
    st.subheader("🔤 Perbandingan Jumlah Kata")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Rata-rata Kata (Original)",
            f"{impact['word_comparison']['original_avg_words']:.1f}"
        )
    
    with col2:
        st.metric(
            "Rata-rata Kata (Processed)",
            f"{impact['word_comparison']['processed_avg_words']:.1f}"
        )
    
    with col3:
        st.metric(
            "Pengurangan Kata",
            f"{impact['word_comparison']['word_reduction_pct']:.1f}%"
        )
    
    # Vocabulary comparison
    st.subheader("📚 Perbandingan Vocabulary")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Vocabulary Size (Original)",
            f"{impact['vocabulary_comparison']['original_vocab_size']:,}"
        )
    
    with col2:
        st.metric(
            "Vocabulary Size (Processed)",
            f"{impact['vocabulary_comparison']['processed_vocab_size']:,}"
        )
    
    with col3:
        st.metric(
            "Pengurangan Vocabulary",
            f"{impact['vocabulary_comparison']['vocab_reduction_pct']:.1f}%"
        )
    
    # Visualization of impact
    st.subheader("📊 Visualisasi Dampak Preprocessing")
    
    # Create comparison charts
    comparison_data = {
        'Metric': ['Avg Characters', 'Avg Words', 'Vocabulary Size'],
        'Original': [
            impact['length_comparison']['original_avg_length'],
            impact['word_comparison']['original_avg_words'],
            impact['vocabulary_comparison']['original_vocab_size']
        ],
        'Processed': [
            impact['length_comparison']['processed_avg_length'],
            impact['word_comparison']['processed_avg_words'],
            impact['vocabulary_comparison']['processed_vocab_size']
        ]
    }
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # Normalize for better visualization
    for metric in ['Avg Characters', 'Avg Words']:
        fig = px.bar(
            comparison_df[comparison_df['Metric'] == metric].melt(
                id_vars=['Metric'], 
                value_vars=['Original', 'Processed'],
                var_name='Type', 
                value_name='Value'
            ),
            x='Type',
            y='Value',
            title=f"Comparison: {metric}",
            color='Type'
        )
        st.plotly_chart(fig, use_container_width=True)

def show_feature_extraction(df):
    """Phase 4: Feature extraction"""
    
    st.header("🔍 Phase 4: Feature Extraction")
    st.markdown("**Tujuan**: Mengekstrak fitur untuk machine learning")
    
    if 'df_processed' not in st.session_state:
        st.warning("⚠️ Please run preprocessing in Phase 2 first")
        return
    
    df_processed = st.session_state.df_processed
    
    if st.button("🔍 Start Feature Extraction", type="primary"):
        
        with st.spinner("🔍 Extracting features... This may take a few minutes."):
            features = extract_features_comprehensive(df_processed, 'ulasan_final_processed')
            
            if features:
                st.session_state.features = features
                st.success("✅ Feature extraction completed!")
                
                # Display feature information
                st.subheader("📊 Feature Extraction Results")
                
                for feature_type, feature_data in features.items():
                    if feature_type == 'linguistic':
                        st.markdown(f"**{feature_type.title()} Features:**")
                        st.dataframe(feature_data.describe(), use_container_width=True)
                    else:
                        st.markdown(f"**{feature_type.replace('_', ' ').title()} Features:**")
                        
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            st.metric("Matrix Shape", f"{feature_data['shape']}")
                        
                        with col2:
                            st.metric("Vocabulary Size", f"{feature_data['vocabulary_size']:,}")
                        
                        with col3:
                            st.metric("Feature Count", f"{len(feature_data['feature_names']):,}")
                        
                        # Show top features
                        if 'feature_names' in feature_data:
                            st.markdown(f"**Top 20 {feature_type.title()} Features:**")
                            top_features = feature_data['feature_names'][:20]
                            
                            # Display in columns
                            cols = st.columns(4)
                            for i, feature in enumerate(top_features):
                                with cols[i % 4]:
                                    st.write(f"• {feature}")

def show_summary_insights(df):
    """Phase 5: Summary and insights"""
    
    st.header("📋 Phase 5: Summary & Insights")
    st.markdown("**Tujuan**: Ringkasan dan insight dari seluruh proses analisis")
    
    # Check if all phases completed
    phases_completed = {
        'Raw Data Analysis': True,  # Always available
        'Text Preprocessing': 'df_processed' in st.session_state,
        'Feature Extraction': 'features' in st.session_state
    }
    
    st.subheader("✅ Progress Analisis")
    
    for phase, completed in phases_completed.items():
        status = "✅ Completed" if completed else "⏳ Pending"
        st.write(f"**{phase}**: {status}")
    
    if all(phases_completed.values()):
        st.success("🎉 All analysis phases completed!")
        
        # Generate comprehensive insights
        st.subheader("🔍 Key Insights")
        
        # Data insights
        st.markdown("**📊 Data Insights:**")
        st.write(f"• Dataset contains {len(df):,} GoFood reviews")
        st.write(f"• Average review length: {df['ulasan'].astype(str).str.len().mean():.1f} characters")
        st.write(f"• Rating distribution: {df['nilai'].value_counts().to_dict()}")
        
        if 'df_processed' in st.session_state:
            df_processed = st.session_state.df_processed
            
            # Preprocessing insights
            st.markdown("**🔄 Preprocessing Insights:**")
            original_avg = df['ulasan'].astype(str).str.len().mean()
            processed_avg = df_processed['ulasan_final_processed'].astype(str).str.len().mean()
            reduction = ((original_avg - processed_avg) / original_avg) * 100
            
            st.write(f"• Text length reduced by {reduction:.1f}% after preprocessing")
            st.write(f"• Removed stopwords, normalized slang, and cleaned punctuation")
            st.write(f"• Final processed texts ready for machine learning")
        
        if 'features' in st.session_state:
            features = st.session_state.features
            
            # Feature extraction insights
            st.markdown("**🔍 Feature Extraction Insights:**")
            
            if 'tfidf_unigram' in features:
                st.write(f"• TF-IDF Unigram: {features['tfidf_unigram']['vocabulary_size']:,} features")
            
            if 'tfidf_bigram' in features:
                st.write(f"• TF-IDF Bigram: {features['tfidf_bigram']['vocabulary_size']:,} features")
            
            if 'linguistic' in features:
                st.write(f"• Linguistic features: {len(features['linguistic'].columns)} features")
        
        # Recommendations
        st.subheader("💡 Recommendations")
        
        st.markdown("**📈 Next Steps:**")
        st.write("• Use extracted features for machine learning model training")
        st.write("• Consider ensemble methods combining different feature types")
        st.write("• Implement cross-validation for robust model evaluation")
        st.write("• Monitor model performance on new data")
        
        # Export processed data
        st.subheader("💾 Export Processed Data")
        
        if 'df_processed' in st.session_state:
            df_processed = st.session_state.df_processed
            
            csv_data = df_processed.to_csv(index=False)
            
            st.download_button(
                label="📥 Download Processed Dataset",
                data=csv_data,
                file_name=f"gofood_processed_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    
    else:
        st.info("💡 Complete all analysis phases to see comprehensive insights")

if __name__ == "__main__":
    main()
