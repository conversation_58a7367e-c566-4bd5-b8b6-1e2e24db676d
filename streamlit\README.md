# 🚀 GoFood Sentiment Analysis Dashboard

Modern, responsive Streamlit dashboard for analyzing sentiment in GoFood Merchant reviews using Machine Learning.

## 📋 Overview

This dashboard provides a comprehensive interface for sentiment analysis of GoFood reviews, featuring:

- **Real-time Prediction**: Analyze sentiment of individual reviews or batch process multiple reviews
- **Interactive Visualizations**: Modern charts and graphs using Plotly
- **Model Comparison**: Compare performance of different ML models
- **Data Exploration**: Comprehensive data analysis and filtering capabilities
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

## 🎯 Features

### 🏠 Dashboard Overview
- Key metrics and statistics
- Data quality indicators
- Quick insights and trends
- System status monitoring

### 📊 Data Explorer
- Interactive data filtering
- Statistical analysis
- Word cloud generation
- Text analysis and preprocessing visualization

### 🤖 Model Comparison
- Performance metrics comparison
- Confusion matrix analysis
- Feature importance visualization
- Model recommendations

### 🔮 Prediction Interface
- Single text prediction with confidence scores
- Batch file upload and processing
- Real-time preprocessing visualization
- Export prediction results

### 📈 Advanced Analytics
- Sentiment trends over time
- Topic modeling and analysis
- Business insights and recommendations
- Comparative analysis

### 📋 Reports
- Executive summary generation
- Technical performance reports
- Export capabilities (PDF, CSV, Excel)
- Automated report scheduling

## 🚀 Quick Start

### Prerequisites

```bash
# Python 3.8 or higher
python --version

# Required data files (in parent directory):
# - reviews_gofood_Merchant.xlsx
# - model_random_forest.pkl
# - model_logistic_regression.pkl
# - best_model.pkl
# - tfidf_vectorizer_tuned.pkl
```

### Installation

1. **Navigate to the streamlit directory:**
   ```bash
   cd streamlit
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the dashboard:**
   ```bash
   streamlit run app.py
   ```

4. **Open your browser:**
   ```
   http://localhost:8501
   ```

## 📁 Project Structure

```
streamlit/
├── 📱 app.py                          # Main Streamlit application
├── 📊 pages/
│   ├── 01_🏠_Dashboard.py             # Main dashboard overview
│   ├── 02_📊_Data_Explorer.py         # Data exploration & EDA
│   ├── 03_🤖_Model_Comparison.py      # Model performance comparison
│   ├── 04_🔮_Prediction.py           # Real-time prediction interface
│   ├── 05_📈_Analytics.py             # Advanced analytics & insights
│   └── 06_📋_Reports.py               # Generate and export reports
├── 🎨 components/
│   ├── __init__.py
│   ├── charts.py                      # Reusable chart components
│   ├── metrics.py                     # Metric display components
│   ├── sidebar.py                     # Sidebar components
│   └── styling.py                     # CSS and styling utilities
├── 🔧 utils/
│   ├── __init__.py
│   ├── data_loader.py                 # Data loading utilities
│   ├── model_utils.py                 # Model loading and prediction
│   ├── preprocessing.py               # Text preprocessing functions
│   └── visualization.py               # Visualization utilities
├── 🔧 config/
│   ├── settings.py                    # Application settings
│   └── constants.py                   # Constants and configurations
├── 📋 requirements.txt                # Python dependencies
└── 📖 README.md                       # This documentation
```

## 🎨 UI/UX Features

### Modern Design
- Clean, minimalist interface
- Consistent color scheme and typography
- Professional business-ready appearance
- Intuitive navigation and controls

### Responsive Layout
- Optimized for desktop, tablet, and mobile
- Flexible grid system
- Adaptive chart sizing
- Touch-friendly controls

### Interactive Elements
- Real-time data filtering
- Dynamic chart updates
- Hover tooltips and information
- Smooth transitions and animations

## 🔧 Configuration

### Application Settings
Edit `config/settings.py` to customize:

```python
# Data file paths
DATA_FILES = {
    'raw_reviews': 'path/to/reviews_gofood_Merchant.xlsx',
    'preprocessed_reviews': 'path/to/gofood_ulasan_preprocessed.xlsx',
}

# Model file paths
MODEL_FILES = {
    'random_forest': 'path/to/model_random_forest.pkl',
    'logistic_regression': 'path/to/model_logistic_regression.pkl',
    'best_model': 'path/to/best_model.pkl',
}

# UI Configuration
UI_CONFIG = {
    'primary_color': '#1f77b4',
    'success_color': '#2ca02c',
    'warning_color': '#ff7f0e',
    'danger_color': '#d62728',
}
```

### Constants
Modify `config/constants.py` for:
- Column name mappings
- Sentiment labels
- Chart configurations
- Default values

## 📊 Data Requirements

### Input Data Format
The dashboard expects the following data structure:

```csv
rating,ulasan
5,"Aplikasi sangat bagus dan mudah digunakan!"
1,"Pelayanan buruk, makanan terlambat datang"
3,"Biasa saja, tidak ada yang istimewa"
```

### Required Columns
- **rating**: Numeric rating (1-5)
- **ulasan**: Review text in Indonesian

### Model Files
- **model_random_forest.pkl**: Trained Random Forest model
- **model_logistic_regression.pkl**: Trained Logistic Regression model
- **best_model.pkl**: Best performing model
- **tfidf_vectorizer_tuned.pkl**: TF-IDF vectorizer

## 🚀 Deployment

### Local Development
```bash
# Run with debug mode
streamlit run app.py --server.runOnSave true

# Run on specific port
streamlit run app.py --server.port 8502
```

### Production Deployment

#### Streamlit Cloud
1. Push code to GitHub repository
2. Connect to Streamlit Cloud
3. Deploy with one click

#### Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8501

CMD ["streamlit", "run", "app.py", "--server.address", "0.0.0.0"]
```

#### Cloud Platforms
- **AWS**: Deploy using EC2, ECS, or Lambda
- **Google Cloud**: Use Cloud Run or Compute Engine
- **Azure**: Deploy with Container Instances or App Service

## 🔍 Troubleshooting

### Common Issues

1. **Data files not found**
   ```
   Error: Data file not found: reviews_gofood_Merchant.xlsx
   ```
   **Solution**: Ensure data files are in the correct directory (parent folder)

2. **Model loading errors**
   ```
   Error: Model file not found: model_random_forest.pkl
   ```
   **Solution**: Check model file paths in `config/settings.py`

3. **Import errors**
   ```
   ModuleNotFoundError: No module named 'streamlit'
   ```
   **Solution**: Install requirements: `pip install -r requirements.txt`

4. **Memory issues with large datasets**
   **Solution**: Implement data pagination or increase system memory

### Performance Optimization

- Use `@st.cache_data` for data loading
- Implement lazy loading for large models
- Optimize chart rendering with sampling
- Use session state for user preferences

## 📚 Documentation

### User Guide
- **Getting Started**: Basic navigation and features
- **Data Upload**: How to upload and format data
- **Model Selection**: Choosing the right model
- **Interpretation**: Understanding results and metrics

### Technical Documentation
- **API Reference**: Function and class documentation
- **Architecture**: System design and components
- **Customization**: How to extend and modify features
- **Integration**: Connecting with external systems

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Streamlit**: For the amazing web app framework
- **Plotly**: For interactive visualizations
- **Scikit-learn**: For machine learning capabilities
- **Pandas**: For data manipulation
- **Indonesian NLP Community**: For language processing resources

## 📞 Support

For support and questions:
- 📧 Email: <EMAIL>
- 💬 Issues: GitHub Issues
- 📖 Documentation: [Link to docs]
- 🎥 Video Tutorials: [Link to videos]

---

**Built with ❤️ for better customer insights**

🚀 **Happy Analyzing!** 📊✨
