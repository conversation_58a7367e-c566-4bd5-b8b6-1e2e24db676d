# 📖 User Guide - GoFood Sentiment Analysis Dashboard

Welcome to the GoFood Sentiment Analysis Dashboard! This guide will help you navigate and use all the features of the dashboard effectively.

## 🚀 Getting Started

### Quick Launch
1. **Navigate to the streamlit directory**
   ```bash
   cd streamlit
   ```

2. **Run the setup (first time only)**
   ```bash
   python setup.py
   ```

3. **Launch the dashboard**
   ```bash
   python launch.py
   ```

4. **Open your browser**
   - The dashboard will automatically open at `http://localhost:8501`
   - If it doesn't open automatically, click the link in the terminal

## 🏠 Dashboard Overview

### Main Navigation
The dashboard is organized into 6 main sections:

- **🏠 Dashboard**: Overview and key metrics
- **📊 Data Explorer**: Interactive data analysis
- **🤖 Model Comparison**: Compare ML model performance
- **🔮 Prediction**: Real-time sentiment prediction
- **📈 Analytics**: Advanced business insights
- **📋 Reports**: Generate and export reports

### Sidebar Controls
- **Navigation menu**: Switch between different pages
- **Filters**: Customize data views and analysis
- **Settings**: Configure dashboard preferences

## 📊 Using Each Section

### 🏠 Dashboard Page

**Purpose**: Get a quick overview of your sentiment analysis data

**Key Features**:
- **Metrics Cards**: Total reviews, average rating, sentiment distribution
- **Data Quality Indicators**: Completeness, validation status
- **Quick Visualizations**: Rating and sentiment charts
- **System Status**: Model availability and processing status

**How to Use**:
1. Check the key metrics at the top
2. Review data quality indicators
3. Examine the visualizations for quick insights
4. Monitor system status for any issues

### 📊 Data Explorer Page

**Purpose**: Explore and analyze your review data in detail

**Key Features**:
- **Interactive Filters**: Filter by rating, sentiment, text length
- **Multiple Visualizations**: Charts, word clouds, distributions
- **Data Search**: Find specific reviews or keywords
- **Export Options**: Download filtered data

**How to Use**:
1. **Set Filters** (Sidebar):
   - Select ratings to include (1-5 stars)
   - Choose sentiment types (Positive, Negative, Neutral)
   - Set text length range

2. **Explore Visualizations**:
   - **Distributions Tab**: View rating and sentiment charts
   - **Text Analysis Tab**: Generate word clouds by sentiment
   - **Detailed View Tab**: Browse individual reviews
   - **Statistics Tab**: View descriptive statistics

3. **Search and Filter**:
   - Use the search box to find specific keywords
   - Select columns to display
   - Use pagination for large datasets

4. **Export Data**:
   - Click "Download filtered data as CSV"
   - Choose which columns to include

### 🤖 Model Comparison Page

**Purpose**: Compare the performance of different machine learning models

**Key Features**:
- **Performance Metrics**: Accuracy, precision, recall, F1-score
- **Confusion Matrices**: Detailed prediction analysis
- **Feature Importance**: See which words matter most
- **Model Recommendations**: Guidance on model selection

**How to Use**:
1. **Performance Metrics Tab**:
   - View comparison table of all models
   - Check the metrics visualization
   - Identify the best performing model

2. **Confusion Matrix Tab**:
   - Select a model to analyze
   - Examine prediction accuracy by class
   - Compare matrices across models

3. **Feature Analysis Tab**:
   - Choose a model for feature analysis
   - View top important words/features
   - Compare feature importance across models

4. **Detailed Comparison Tab**:
   - Review model parameters
   - Read model recommendations
   - Understand pros and cons of each model

### 🔮 Prediction Page

**Purpose**: Predict sentiment for new reviews in real-time

**Key Features**:
- **Single Text Prediction**: Analyze individual reviews
- **Batch Processing**: Upload CSV files for bulk analysis
- **Confidence Scores**: See prediction certainty
- **Preprocessing Visualization**: Understand text processing steps

**How to Use**:

#### Single Text Prediction:
1. **Select Model** (Sidebar): Choose which model to use
2. **Enter Text**: 
   - Type or paste review text
   - Or select a sample text for testing
3. **Configure Options** (Sidebar):
   - Show preprocessing steps
   - Display confidence scores
4. **Predict**: Click "Predict Sentiment"
5. **Review Results**:
   - See predicted sentiment
   - Check confidence score
   - View probability distribution
6. **Export**: Download results as CSV

#### Batch Prediction:
1. **Upload File**: Choose CSV file with review texts
2. **Select Column**: Pick the column containing reviews
3. **Set Batch Size**: Choose processing batch size
4. **Process**: Click "Process Batch Prediction"
5. **Review Results**:
   - View summary statistics
   - Examine visualizations
   - Browse detailed results
6. **Export**: Download complete results

### 📈 Analytics Page

**Purpose**: Gain deep business insights from sentiment analysis

**Key Features**:
- **Business KPIs**: Customer satisfaction, retention risk
- **Trend Analysis**: Sentiment changes over time
- **Topic Analysis**: Common themes and keywords
- **Actionable Recommendations**: Business improvement suggestions

**How to Use**:
1. **Business Insights Tab**:
   - Review key performance indicators
   - Check customer satisfaction metrics
   - Assess business impact levels

2. **Trend Analysis Tab**:
   - Select time period for analysis
   - View sentiment trends over time
   - Analyze daily patterns

3. **Topic Analysis Tab**:
   - Generate word clouds for different sentiments
   - Identify positive and negative themes
   - Understand common customer concerns

4. **Recommendations Tab**:
   - Filter by priority level
   - Review specific recommendations
   - Plan action items based on insights

### 📋 Reports Page

**Purpose**: Generate and export comprehensive reports

**Key Features**:
- **Multiple Report Types**: Executive, technical, quality, performance
- **Customizable Content**: Choose what to include
- **Export Formats**: Text, CSV, PDF options
- **Automated Generation**: Quick report creation

**How to Use**:
1. **Select Report Type**:
   - **Executive Summary**: High-level business overview
   - **Technical Analysis**: Detailed data analysis
   - **Data Quality**: Data completeness and issues
   - **Model Performance**: ML model comparison
   - **Custom Report**: Build your own report

2. **Configure Options** (Sidebar):
   - Include visualizations
   - Add raw data
   - Include recommendations
   - Set report period

3. **Generate Report**: Content appears automatically
4. **Export Options**:
   - Download as text file
   - Export summary as CSV
   - Save full dataset

## 💡 Tips and Best Practices

### Data Quality
- **Check data quality indicators** on the Dashboard page
- **Use filters** to focus on relevant data
- **Review missing values** in the Data Explorer

### Model Selection
- **Compare all models** before choosing one for production
- **Consider both accuracy and interpretability**
- **Use the "Best Model" for highest accuracy**

### Prediction Accuracy
- **Use longer, well-written reviews** for better predictions
- **Check confidence scores** - higher is more reliable
- **Validate predictions** with known sentiment examples

### Business Insights
- **Focus on high-priority recommendations** first
- **Monitor trends regularly** to catch issues early
- **Use positive feedback** for marketing opportunities

## 🔧 Troubleshooting

### Common Issues

**Dashboard won't load**:
- Check if all required files are in the parent directory
- Verify Python version (3.8+ required)
- Run `python setup.py` to reinstall dependencies

**Models not available**:
- Ensure model files (.pkl) exist in parent directory
- Check file permissions
- Verify model files aren't corrupted

**Prediction errors**:
- Check text length (minimum 5 characters)
- Ensure text is in Indonesian language
- Try with sample texts first

**Slow performance**:
- Reduce batch size for large uploads
- Use filters to limit data size
- Close other browser tabs

**Export not working**:
- Check browser download settings
- Ensure sufficient disk space
- Try different export formats

### Getting Help

1. **Check the README.md** for detailed setup instructions
2. **Review error messages** in the terminal/console
3. **Try the sample data** to verify functionality
4. **Restart the dashboard** if issues persist

## 📚 Additional Resources

### Documentation
- **README.md**: Complete setup and technical documentation
- **DEVELOPMENT_PLAN.md**: Technical architecture details
- **requirements.txt**: List of required Python packages

### Sample Data
- Use the provided sample texts in the Prediction page
- Download sample CSV format for batch processing
- Check the Data Explorer for data format examples

### Support
- Review error messages for specific guidance
- Check system requirements and compatibility
- Verify all data files are properly formatted

---

**Happy Analyzing! 📊✨**

*This dashboard is designed to help you gain valuable insights from customer feedback and improve your business decisions.*
