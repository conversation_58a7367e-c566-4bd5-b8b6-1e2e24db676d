"""
Main Streamlit Application for GoFood Sentiment Analysis Dashboard
"""

import streamlit as st
import sys
from pathlib import Path

# Add the parent directory to the path to import from the main project
sys.path.append(str(Path(__file__).parent.parent))

# Import configuration and components
from config.settings import APP_CONFIG
from components.styling import load_css, create_header, add_footer
from utils.data_loader import load_raw_data, load_preprocessed_data, validate_data

def main():
    """Main application function"""
    
    # Configure Streamlit page
    st.set_page_config(
        page_title=APP_CONFIG['page_title'],
        page_icon=APP_CONFIG['page_icon'],
        layout=APP_CONFIG['layout'],
        initial_sidebar_state=APP_CONFIG['initial_sidebar_state']
    )
    
    # Load custom CSS
    load_css()
    
    # Create main header
    create_header(
        title="🚀 GoFood Sentiment Analysis Dashboard",
        subtitle="Analisis Sentimen U<PERSON>an GoFood Merchant dengan Machine Learning"
    )
    
    # Sidebar navigation
    st.sidebar.title("📊 Navigation")
    st.sidebar.markdown("---")
    
    # Navigation menu
    pages = {
        "🏠 Dashboard": "dashboard",
        "📊 Data Explorer": "data_explorer",
        "🔬 Comprehensive Analysis": "comprehensive_analysis",
        "🤖 Model Comparison": "model_comparison",
        "🔮 Prediction": "prediction",
        "📈 Analytics": "analytics",
        "📋 Reports": "reports"
    }
    
    selected_page = st.sidebar.selectbox(
        "Pilih Halaman:",
        list(pages.keys()),
        index=0
    )
    
    # Display selected page
    page_key = pages[selected_page]
    
    if page_key == "dashboard":
        show_dashboard()
    elif page_key == "data_explorer":
        show_data_explorer()
    elif page_key == "comprehensive_analysis":
        show_comprehensive_analysis()
    elif page_key == "model_comparison":
        show_model_comparison()
    elif page_key == "prediction":
        show_prediction()
    elif page_key == "analytics":
        show_analytics()
    elif page_key == "reports":
        show_reports()
    
    # Add footer
    add_footer()

def show_dashboard():
    """Show main dashboard overview"""
    st.header("📊 Dashboard Overview")
    
    # Load data for overview
    with st.spinner("Loading data..."):
        df_raw = load_raw_data()
        df_processed = load_preprocessed_data()
    
    if df_raw is not None:
        # Data validation
        validation = validate_data(df_raw)
        
        if not validation['is_valid']:
            st.error("❌ Data validation failed:")
            for error in validation['errors']:
                st.error(f"• {error}")
            return
        
        # Display warnings if any
        if validation['warnings']:
            st.warning("⚠️ Data warnings:")
            for warning in validation['warnings']:
                st.warning(f"• {warning}")
        
        # Overview metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="📝 Total Reviews",
                value=f"{len(df_raw):,}",
                delta=None
            )
        
        with col2:
            if 'rating' in df_raw.columns:
                avg_rating = df_raw['rating'].mean()
                st.metric(
                    label="⭐ Average Rating",
                    value=f"{avg_rating:.2f}",
                    delta=None
                )
            else:
                st.metric(
                    label="⭐ Average Rating",
                    value="N/A",
                    delta=None
                )
        
        with col3:
            if df_processed is not None:
                st.metric(
                    label="🔄 Processed Reviews",
                    value=f"{len(df_processed):,}",
                    delta=f"{len(df_processed) - len(df_raw):+,}"
                )
            else:
                st.metric(
                    label="🔄 Processed Reviews",
                    value="0",
                    delta=None
                )
        
        with col4:
            # Calculate data quality score
            missing_pct = (df_raw.isnull().sum().sum() / (len(df_raw) * len(df_raw.columns))) * 100
            quality_score = max(0, 100 - missing_pct)
            st.metric(
                label="📊 Data Quality",
                value=f"{quality_score:.1f}%",
                delta=None
            )
        
        st.markdown("---")
        
        # Quick insights
        st.subheader("🔍 Quick Insights")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.info("📈 **Data Overview**")
            st.write(f"• Total records: {len(df_raw):,}")
            st.write(f"• Columns: {len(df_raw.columns)}")
            if 'rating' in df_raw.columns:
                rating_dist = df_raw['rating'].value_counts().sort_index()
                st.write(f"• Rating range: {rating_dist.index.min()} - {rating_dist.index.max()}")
        
        with col2:
            st.success("✅ **System Status**")
            st.write("• Data loading: ✅ Success")
            st.write(f"• Data validation: {'✅ Passed' if validation['is_valid'] else '❌ Failed'}")
            st.write(f"• Models available: 🔄 Checking...")
        
        # Recent activity (placeholder)
        st.subheader("📋 Recent Activity")
        st.info("🚧 Recent predictions and analysis will be displayed here")
        
    else:
        st.error("❌ Failed to load data. Please check if the data files exist.")
        st.info("📁 Expected data files:")
        st.code("""
        • reviews_gofood_Merchant.xlsx (raw data)
        • gofood_ulasan_preprocessed.xlsx (processed data)
        """)

def show_data_explorer():
    """Show data exploration page"""
    st.header("📊 Data Explorer")
    st.info("🚧 Data exploration features will be implemented here")

def show_comprehensive_analysis():
    """Show comprehensive analysis page"""
    st.header("🔬 Comprehensive Analysis")
    st.info("🚧 Comprehensive analysis features will be implemented here")

def show_model_comparison():
    """Show model comparison page"""
    st.header("🤖 Model Comparison")
    st.info("🚧 Model comparison features will be implemented here")

def show_prediction():
    """Show prediction interface"""
    st.header("🔮 Prediction Interface")
    st.info("🚧 Real-time prediction features will be implemented here")

def show_analytics():
    """Show advanced analytics"""
    st.header("📈 Advanced Analytics")
    st.info("🚧 Advanced analytics features will be implemented here")

def show_reports():
    """Show reports page"""
    st.header("📋 Reports")
    st.info("🚧 Report generation features will be implemented here")

if __name__ == "__main__":
    main()
