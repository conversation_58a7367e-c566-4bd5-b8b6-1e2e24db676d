/* Custom CSS for GoFood Sentiment Analysis Dashboard */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Root variables */
:root {
    --primary-color: #1f77b4;
    --success-color: #2ca02c;
    --warning-color: #ff7f0e;
    --danger-color: #d62728;
    --neutral-color: #7f7f7f;
    --background-color: #ffffff;
    --sidebar-background: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #e1e5e9;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Global styles */
* {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Main container */
.main .block-container {
    padding-top: 2rem;
    padding-bottom: 2rem;
    max-width: 1200px;
}

/* Header styles */
.main-header {
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
}

.main-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.main-header p {
    margin: 0.5rem 0 0 0;
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Card styles */
.metric-card {
    background: var(--background-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    text-align: center;
    box-shadow: var(--shadow);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.metric-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.metric-title {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.metric-delta {
    font-size: 0.8rem;
    font-weight: 500;
}

.metric-delta.positive {
    color: var(--success-color);
}

.metric-delta.negative {
    color: var(--danger-color);
}

/* Info cards */
.info-card {
    background: var(--background-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.info-card:hover {
    box-shadow: var(--shadow-hover);
}

.info-card.success-card {
    border-left-color: var(--success-color);
}

.info-card.warning-card {
    border-left-color: var(--warning-color);
}

.info-card.danger-card {
    border-left-color: var(--danger-color);
}

/* Button styles */
.stButton > button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stButton > button:hover {
    background: #1565c0;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stButton > button:active {
    transform: translateY(0);
}

/* Success button variant */
.success-button > button {
    background: var(--success-color);
}

.success-button > button:hover {
    background: #1e7e34;
}

/* Warning button variant */
.warning-button > button {
    background: var(--warning-color);
}

.warning-button > button:hover {
    background: #e68900;
}

/* Danger button variant */
.danger-button > button {
    background: var(--danger-color);
}

.danger-button > button:hover {
    background: #bd2130;
}

/* Sidebar styles */
.css-1d391kg {
    background: var(--sidebar-background);
    border-right: 1px solid var(--border-color);
}

.css-1d391kg .css-1v0mbdj {
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

/* Navigation styles */
.nav-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: block;
    margin-bottom: 0.5rem;
}

.nav-link:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
}

/* Form elements */
.stSelectbox > div > div > div {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.stSelectbox > div > div > div:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(31, 119, 180, 0.25);
}

.stTextInput > div > div > input {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    transition: var(--transition);
}

.stTextInput > div > div > input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(31, 119, 180, 0.25);
    outline: none;
}

.stTextArea > div > div > textarea {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    transition: var(--transition);
    font-family: inherit;
}

.stTextArea > div > div > textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(31, 119, 180, 0.25);
    outline: none;
}

/* File uploader */
.stFileUploader > div > div > div {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    background: var(--background-color);
}

.stFileUploader > div > div > div:hover {
    border-color: var(--primary-color);
    background: #f8f9fa;
}

/* Progress bar */
.stProgress > div > div > div > div {
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

/* Dataframe styles */
.dataframe {
    border: none !important;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.dataframe thead th {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.8rem;
}

.dataframe tbody tr:nth-child(even) {
    background: #f8f9fa !important;
}

.dataframe tbody tr:hover {
    background: #e9ecef !important;
}

.dataframe tbody td {
    border: none !important;
    padding: 0.75rem !important;
}

/* Chart container */
.chart-container {
    background: var(--background-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
}

/* Expander styles */
.streamlit-expanderHeader {
    background: var(--sidebar-background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.streamlit-expanderHeader:hover {
    background: #e9ecef;
}

/* Alert styles */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid transparent;
    font-weight: 500;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Loading spinner */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* Footer */
.footer {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
    border-top: 1px solid var(--border-color);
    margin-top: 3rem;
    background: var(--sidebar-background);
    border-radius: var(--border-radius);
}

/* Responsive design */
@media (max-width: 768px) {
    .main .block-container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .main-header {
        padding: 1.5rem;
    }
    
    .main-header h1 {
        font-size: 2rem;
    }
    
    .metric-card {
        padding: 1rem;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .main-header h1 {
        font-size: 1.5rem;
    }
    
    .metric-value {
        font-size: 1.25rem;
    }
    
    .stButton > button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

/* Hide Streamlit branding */
#MainMenu {
    visibility: hidden;
}

footer {
    visibility: hidden;
}

header {
    visibility: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--neutral-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }

.rounded { border-radius: var(--border-radius); }
.shadow { box-shadow: var(--shadow); }
.shadow-hover { box-shadow: var(--shadow-hover); }
