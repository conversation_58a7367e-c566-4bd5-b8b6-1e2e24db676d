"""
Styling utilities for the GoFood Sentiment Analysis Dashboard
"""

import streamlit as st
from config.settings import UI_CONFIG

def load_css():
    """Load custom CSS styles"""
    css = f"""
    <style>
    /* Main app styling */
    .main .block-container {{
        padding-top: 2rem;
        padding-bottom: 2rem;
        max-width: 1200px;
    }}
    
    /* Header styling */
    .main-header {{
        background: linear-gradient(90deg, {UI_CONFIG['primary_color']}, {UI_CONFIG['success_color']});
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
    }}
    
    /* Metric cards */
    .metric-card {{
        background-color: {UI_CONFIG['background_color']};
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #e1e5e9;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }}
    
    .metric-title {{
        color: {UI_CONFIG['primary_color']};
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }}
    
    .metric-value {{
        color: #212529;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }}
    
    .metric-delta {{
        font-size: 0.8rem;
        font-weight: 500;
    }}
    
    .metric-delta.positive {{
        color: {UI_CONFIG['success_color']};
    }}
    
    .metric-delta.negative {{
        color: {UI_CONFIG['danger_color']};
    }}
    
    /* Sidebar styling */
    .css-1d391kg {{
        background-color: {UI_CONFIG['sidebar_background']};
    }}
    
    /* Button styling */
    .stButton > button {{
        background-color: {UI_CONFIG['primary_color']};
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }}
    
    .stButton > button:hover {{
        background-color: #1565c0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }}
    
    /* Success button */
    .success-button > button {{
        background-color: {UI_CONFIG['success_color']};
    }}
    
    .success-button > button:hover {{
        background-color: #1e7e34;
    }}
    
    /* Warning button */
    .warning-button > button {{
        background-color: {UI_CONFIG['warning_color']};
    }}
    
    .warning-button > button:hover {{
        background-color: #e68900;
    }}
    
    /* Danger button */
    .danger-button > button {{
        background-color: {UI_CONFIG['danger_color']};
    }}
    
    .danger-button > button:hover {{
        background-color: #bd2130;
    }}
    
    /* Card styling */
    .info-card {{
        background-color: {UI_CONFIG['background_color']};
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid {UI_CONFIG['primary_color']};
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}
    
    .success-card {{
        border-left-color: {UI_CONFIG['success_color']};
    }}
    
    .warning-card {{
        border-left-color: {UI_CONFIG['warning_color']};
    }}
    
    .danger-card {{
        border-left-color: {UI_CONFIG['danger_color']};
    }}
    
    /* Table styling */
    .dataframe {{
        border: none !important;
    }}
    
    .dataframe thead th {{
        background-color: {UI_CONFIG['primary_color']} !important;
        color: white !important;
        border: none !important;
    }}
    
    .dataframe tbody tr:nth-child(even) {{
        background-color: #f8f9fa !important;
    }}
    
    /* Chart container */
    .chart-container {{
        background-color: {UI_CONFIG['background_color']};
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #e1e5e9;
        margin-bottom: 1rem;
    }}
    
    /* Navigation styling */
    .nav-link {{
        color: {UI_CONFIG['primary_color']};
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        transition: all 0.3s ease;
    }}
    
    .nav-link:hover {{
        background-color: {UI_CONFIG['primary_color']};
        color: white;
    }}
    
    .nav-link.active {{
        background-color: {UI_CONFIG['primary_color']};
        color: white;
    }}
    
    /* Progress bar */
    .stProgress > div > div > div > div {{
        background-color: {UI_CONFIG['primary_color']};
    }}
    
    /* Selectbox styling */
    .stSelectbox > div > div > div {{
        background-color: {UI_CONFIG['background_color']};
        border: 1px solid #e1e5e9;
        border-radius: 5px;
    }}
    
    /* Text input styling */
    .stTextInput > div > div > input {{
        border: 1px solid #e1e5e9;
        border-radius: 5px;
        padding: 0.5rem;
    }}
    
    .stTextInput > div > div > input:focus {{
        border-color: {UI_CONFIG['primary_color']};
        box-shadow: 0 0 0 0.2rem rgba(31, 119, 180, 0.25);
    }}
    
    /* File uploader */
    .stFileUploader > div > div > div {{
        border: 2px dashed #e1e5e9;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }}
    
    .stFileUploader > div > div > div:hover {{
        border-color: {UI_CONFIG['primary_color']};
        background-color: #f8f9fa;
    }}
    
    /* Expander styling */
    .streamlit-expanderHeader {{
        background-color: {UI_CONFIG['sidebar_background']};
        border-radius: 5px;
        border: 1px solid #e1e5e9;
    }}
    
    /* Alert styling */
    .alert {{
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        border: 1px solid transparent;
    }}
    
    .alert-info {{
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }}
    
    .alert-success {{
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }}
    
    .alert-warning {{
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }}
    
    .alert-danger {{
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }}
    
    /* Loading spinner */
    .loading-spinner {{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }}
    
    /* Footer */
    .footer {{
        text-align: center;
        padding: 2rem;
        color: {UI_CONFIG['neutral_color']};
        border-top: 1px solid #e1e5e9;
        margin-top: 3rem;
    }}
    
    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    
    /* Custom scrollbar */
    ::-webkit-scrollbar {{
        width: 8px;
    }}
    
    ::-webkit-scrollbar-track {{
        background: #f1f1f1;
        border-radius: 10px;
    }}
    
    ::-webkit-scrollbar-thumb {{
        background: {UI_CONFIG['neutral_color']};
        border-radius: 10px;
    }}
    
    ::-webkit-scrollbar-thumb:hover {{
        background: {UI_CONFIG['primary_color']};
    }}
    </style>
    """
    
    st.markdown(css, unsafe_allow_html=True)

def create_header(title: str, subtitle: str = None):
    """Create styled header"""
    subtitle_html = f"<p style='font-size: 1.2rem; margin: 0; opacity: 0.9;'>{subtitle}</p>" if subtitle else ""
    
    header_html = f"""
    <div class="main-header">
        <h1 style="margin: 0; font-size: 2.5rem; font-weight: 700;">{title}</h1>
        {subtitle_html}
    </div>
    """
    
    st.markdown(header_html, unsafe_allow_html=True)

def create_metric_card(title: str, value: str, delta: str = None, delta_color: str = None):
    """Create styled metric card"""
    delta_html = ""
    if delta:
        if delta_color is None:
            delta_color = "positive" if delta.startswith('+') else "negative"
        delta_html = f'<div class="metric-delta {delta_color}">{delta}</div>'
    
    card_html = f"""
    <div class="metric-card">
        <div class="metric-title">{title}</div>
        <div class="metric-value">{value}</div>
        {delta_html}
    </div>
    """
    
    st.markdown(card_html, unsafe_allow_html=True)

def create_info_card(content: str, card_type: str = "info"):
    """Create styled info card"""
    card_html = f"""
    <div class="info-card {card_type}-card">
        {content}
    </div>
    """
    
    st.markdown(card_html, unsafe_allow_html=True)

def create_alert(message: str, alert_type: str = "info"):
    """Create styled alert"""
    alert_html = f"""
    <div class="alert alert-{alert_type}">
        {message}
    </div>
    """
    
    st.markdown(alert_html, unsafe_allow_html=True)

def add_footer():
    """Add styled footer"""
    footer_html = """
    <div class="footer">
        <p>🚀 GoFood Sentiment Analysis Dashboard | Powered by Streamlit & Machine Learning</p>
        <p>© 2025 - Built with ❤️ for better customer insights</p>
    </div>
    """
    
    st.markdown(footer_html, unsafe_allow_html=True)
