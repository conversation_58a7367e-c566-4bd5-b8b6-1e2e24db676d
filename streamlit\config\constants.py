"""
Constants for the GoFood Sentiment Analysis Dashboard
"""

# Column names
COLUMNS = {
    'review': 'ulasan',
    'rating': 'nilai',  # Changed from 'rating' to 'nilai' to match actual data
    'sentiment': 'sentiment',
    'processed_text': 'final_processed_text',
    'original_text': 'original_text',
}

# Sentiment labels
SENTIMENT_LABELS = {
    'positive': 'Positif',
    'negative': 'Negatif',
    'neutral': 'Netral',
}

# Model names
MODEL_NAMES = {
    'random_forest': 'Random Forest',
    'best_model': 'Best Model (Random Forest)',
}

# Navigation menu
MENU_ITEMS = [
    "🏠 Dashboard",
    "📊 Data Explorer", 
    "🤖 Model Comparison",
    "🔮 Prediction",
    "📈 Analytics",
    "📋 Reports"
]

# Chart types
CHART_TYPES = {
    'bar': 'Bar Chart',
    'pie': 'Pie Chart',
    'line': 'Line Chart',
    'scatter': 'Scatter Plot',
    'histogram': 'Histogram',
    'box': 'Box Plot',
    'heatmap': 'Heatmap',
}

# Export formats
EXPORT_FORMATS = {
    'csv': 'CSV',
    'excel': 'Excel',
    'json': 'JSON',
    'pdf': 'PDF',
}

# Rating mappings
RATING_SENTIMENT_MAP = {
    1: 'Negatif',
    2: 'Negatif', 
    3: 'Netral',
    4: 'Positif',
    5: 'Positif',
}

# Text preprocessing steps
PREPROCESSING_STEPS = [
    'Text Cleaning',
    'Normalization',
    'Stopword Removal',
    'Stemming',
]

# Metrics for model evaluation
EVALUATION_METRICS = [
    'accuracy',
    'precision',
    'recall',
    'f1_score',
    'roc_auc',
]

# Default values
DEFAULTS = {
    'sample_size': 1000,
    'test_size': 0.2,
    'random_state': 42,
    'n_estimators': 100,
    'max_depth': 10,
}
