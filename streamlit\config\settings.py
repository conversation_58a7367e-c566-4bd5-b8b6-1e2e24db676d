"""
Configuration settings for the GoFood Sentiment Analysis Dashboard
"""

import os
from pathlib import Path

# Base paths
BASE_DIR = Path(__file__).parent.parent
PROJECT_ROOT = BASE_DIR.parent
DATA_DIR = PROJECT_ROOT
MODELS_DIR = PROJECT_ROOT

# Data files
DATA_FILES = {
    'raw_reviews': DATA_DIR / 'reviews_gofood_Merchant.xlsx',
    'preprocessed_reviews': DATA_DIR / 'gofood_ulasan_preprocessed.xlsx',
}

# Model files
MODEL_FILES = {
    'random_forest': MODELS_DIR / 'model_random_forest.pkl',
    'logistic_regression': MODELS_DIR / 'model_logistic_regression.pkl',
    'best_model': MODELS_DIR / 'best_model.pkl',
    'tfidf_vectorizer': MODELS_DIR / 'tfidf_vectorizer_tuned.pkl',
}

# App configuration
APP_CONFIG = {
    'title': '🚀 GoFood Sentiment Analysis Dashboard',
    'page_title': 'GoFood Sentiment Analysis',
    'page_icon': '🚀',
    'layout': 'wide',
    'initial_sidebar_state': 'expanded',
}

# UI Configuration
UI_CONFIG = {
    'primary_color': '#1f77b4',
    'success_color': '#2ca02c',
    'warning_color': '#ff7f0e',
    'danger_color': '#d62728',
    'neutral_color': '#7f7f7f',
    'background_color': '#ffffff',
    'sidebar_background': '#f8f9fa',
}

# Chart configuration
CHART_CONFIG = {
    'height': 400,
    'template': 'plotly_white',
    'color_palette': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
}

# Text processing configuration
TEXT_CONFIG = {
    'max_text_length': 1000,
    'min_text_length': 5,
    'max_batch_size': 1000,
}

# Cache configuration
CACHE_CONFIG = {
    'ttl': 3600,  # 1 hour
    'max_entries': 1000,
}

# Performance settings
PERFORMANCE_CONFIG = {
    'chunk_size': 1000,
    'max_memory_usage': 500,  # MB
    'lazy_loading': True,
}
