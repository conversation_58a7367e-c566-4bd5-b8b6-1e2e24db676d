#!/usr/bin/env python3
"""
Launch script for GoFood Sentiment Analysis Dashboard
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_requirements():
    """Check if required packages are installed"""
    required_packages = [
        'streamlit',
        'pandas',
        'numpy',
        'plotly',
        'scikit-learn',
        'matplotlib',
        'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    return missing_packages

def install_requirements():
    """Install required packages"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        print("📦 Installing requirements...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_data_files():
    """Check if required data files exist"""
    parent_dir = Path(__file__).parent.parent
    
    required_files = [
        "reviews_gofood_Merchant.xlsx",
        "model_random_forest.pkl",
        "tfidf_vectorizer_tuned.pkl"
    ]
    
    missing_files = []
    
    for file_name in required_files:
        file_path = parent_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            missing_files.append(file_name)
            print(f"❌ {file_name}")
    
    return missing_files

def open_browser(url):
    """Open browser to the dashboard URL"""
    system = platform.system()
    
    try:
        if system == "Windows":
            os.startfile(url)
        elif system == "Darwin":  # macOS
            subprocess.run(["open", url])
        else:  # Linux
            subprocess.run(["xdg-open", url])
        
        print(f"🌐 Opening browser to {url}")
    except Exception as e:
        print(f"⚠️ Could not open browser automatically: {e}")
        print(f"Please open your browser and go to: {url}")

def launch_dashboard():
    """Launch the Streamlit dashboard"""
    app_file = Path(__file__).parent / "app.py"
    
    if not app_file.exists():
        print("❌ app.py not found")
        return False
    
    try:
        print("🚀 Launching GoFood Sentiment Analysis Dashboard...")
        print("📊 Dashboard will be available at: http://localhost:8501")
        print("⏹️ Press Ctrl+C to stop the dashboard")
        print("-" * 50)
        
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", str(app_file),
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n⏹️ Dashboard stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch dashboard: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

def main():
    """Main launch function"""
    print("🚀 GoFood Sentiment Analysis Dashboard Launcher")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    print("\n📦 Checking requirements...")
    missing_packages = check_requirements()
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        
        install_choice = input("\n📥 Install missing packages? (y/n): ").lower().strip()
        
        if install_choice in ['y', 'yes']:
            if not install_requirements():
                sys.exit(1)
        else:
            print("❌ Cannot proceed without required packages")
            sys.exit(1)
    
    print("\n📁 Checking data files...")
    missing_files = check_data_files()
    
    if missing_files:
        print(f"\n⚠️ Missing data files: {', '.join(missing_files)}")
        print("📝 Note: Some features may not work without these files")
        
        continue_choice = input("\n🔄 Continue anyway? (y/n): ").lower().strip()
        
        if continue_choice not in ['y', 'yes']:
            print("❌ Launch cancelled")
            sys.exit(1)
    
    print("\n✅ All checks passed!")
    print("\n🚀 Starting dashboard...")
    
    # Launch the dashboard
    launch_dashboard()

if __name__ == "__main__":
    main()
