"""
Main Dashboard Page for GoFood Sentiment Analysis
"""

import streamlit as st
import pandas as pd
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from streamlit.config.settings import APP_CONFIG
from streamlit.components.styling import load_css, create_header, create_metric_card, create_info_card
from streamlit.utils.data_loader import load_raw_data, load_preprocessed_data, validate_data, get_data_summary, add_sentiment_labels
from streamlit.utils.visualization import create_sentiment_distribution_chart, create_rating_distribution_chart

def main():
    """Main dashboard page"""
    
    # Configure page
    st.set_page_config(
        page_title="Dashboard - GoFood Sentiment Analysis",
        page_icon="🏠",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="🏠 Dashboard Overview",
        subtitle="Ringkasan Analisis Sentimen GoFood Merchant"
    )
    
    # Load data
    with st.spinner("🔄 Loading data..."):
        df_raw = load_raw_data()
        df_processed = load_preprocessed_data()
    
    if df_raw is None:
        st.error("❌ Failed to load data. Please check if the data files exist.")
        st.stop()
    
    # Add sentiment labels to raw data
    df_with_sentiment = add_sentiment_labels(df_raw)
    
    # Data validation
    validation = validate_data(df_raw)
    
    # Display validation results
    if not validation['is_valid']:
        st.error("❌ Data Validation Failed")
        for error in validation['errors']:
            st.error(f"• {error}")
        return
    
    if validation['warnings']:
        with st.expander("⚠️ Data Warnings", expanded=False):
            for warning in validation['warnings']:
                st.warning(f"• {warning}")
    
    # Overview Metrics
    st.subheader("📊 Key Metrics")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_reviews = len(df_raw)
        st.metric(
            label="📝 Total Reviews",
            value=f"{total_reviews:,}",
            help="Total number of reviews in the dataset"
        )
    
    with col2:
        if 'rating' in df_raw.columns:
            avg_rating = df_raw['rating'].mean()
            st.metric(
                label="⭐ Average Rating",
                value=f"{avg_rating:.2f}",
                help="Average rating across all reviews"
            )
        else:
            st.metric(label="⭐ Average Rating", value="N/A")
    
    with col3:
        if df_processed is not None:
            processed_count = len(df_processed)
            delta = processed_count - total_reviews
            st.metric(
                label="🔄 Processed Reviews",
                value=f"{processed_count:,}",
                delta=f"{delta:+,}" if delta != 0 else None,
                help="Number of successfully processed reviews"
            )
        else:
            st.metric(label="🔄 Processed Reviews", value="0")
    
    with col4:
        # Calculate data quality score
        total_cells = len(df_raw) * len(df_raw.columns)
        missing_cells = df_raw.isnull().sum().sum()
        quality_score = ((total_cells - missing_cells) / total_cells) * 100
        
        st.metric(
            label="📊 Data Quality",
            value=f"{quality_score:.1f}%",
            help="Percentage of non-missing values in the dataset"
        )
    
    st.markdown("---")
    
    # Charts Section
    st.subheader("📈 Data Visualizations")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Rating Distribution
        if 'rating' in df_raw.columns:
            fig_rating = create_rating_distribution_chart(df_raw, 'rating')
            st.plotly_chart(fig_rating, use_container_width=True)
        else:
            st.info("Rating column not found for visualization")
    
    with col2:
        # Sentiment Distribution (if sentiment labels exist)
        if 'sentiment_label' in df_with_sentiment.columns:
            fig_sentiment = create_sentiment_distribution_chart(df_with_sentiment, 'sentiment_label')
            st.plotly_chart(fig_sentiment, use_container_width=True)
        else:
            st.info("Sentiment labels not available for visualization")
    
    st.markdown("---")
    
    # Data Summary Section
    st.subheader("📋 Data Summary")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**📊 Dataset Information**")
        summary = get_data_summary(df_raw)
        
        info_content = f"""
        <strong>Total Records:</strong> {summary.get('total_records', 'N/A'):,}<br>
        <strong>Total Columns:</strong> {len(summary.get('columns', []))}<br>
        <strong>Data Types:</strong> {len(set(summary.get('data_types', {}).values()))} unique types<br>
        <strong>Missing Values:</strong> {sum(summary.get('missing_values', {}).values())} total
        """
        
        create_info_card(info_content, "info")
    
    with col2:
        st.markdown("**⚡ Quick Stats**")
        
        if 'rating' in df_raw.columns:
            rating_stats = df_raw['rating'].describe()
            stats_content = f"""
            <strong>Rating Statistics:</strong><br>
            • Min: {rating_stats['min']}<br>
            • Max: {rating_stats['max']}<br>
            • Mean: {rating_stats['mean']:.2f}<br>
            • Std: {rating_stats['std']:.2f}
            """
        else:
            stats_content = "<strong>Rating statistics not available</strong>"
        
        create_info_card(stats_content, "success")
    
    # Recent Activity Section
    st.subheader("📋 Recent Activity")
    
    # Show sample of recent data
    if not df_raw.empty:
        st.markdown("**🔍 Sample Data Preview**")
        
        # Display first few rows
        display_columns = ['rating', 'ulasan'] if 'ulasan' in df_raw.columns else df_raw.columns[:3]
        sample_data = df_raw[display_columns].head(5)
        
        st.dataframe(
            sample_data,
            use_container_width=True,
            hide_index=True
        )
        
        with st.expander("📊 View Full Dataset Info"):
            st.write("**Column Information:**")
            col_info = pd.DataFrame({
                'Column': df_raw.columns,
                'Data Type': df_raw.dtypes,
                'Non-Null Count': df_raw.count(),
                'Null Count': df_raw.isnull().sum()
            })
            st.dataframe(col_info, use_container_width=True)
    
    # System Status
    st.markdown("---")
    st.subheader("🔧 System Status")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status_content = f"""
        <strong>Data Loading:</strong> ✅ Success<br>
        <strong>Validation:</strong> {'✅ Passed' if validation['is_valid'] else '❌ Failed'}<br>
        <strong>Processing:</strong> {'✅ Available' if df_processed is not None else '⚠️ Pending'}
        """
        create_info_card(status_content, "success")
    
    with col2:
        model_content = """
        <strong>Models Status:</strong><br>
        • Random Forest: 🔄 Checking...<br>
        • Logistic Regression: 🔄 Checking...<br>
        • Best Model: 🔄 Checking...
        """
        create_info_card(model_content, "warning")
    
    with col3:
        performance_content = f"""
        <strong>Performance:</strong><br>
        • Load Time: < 3s<br>
        • Memory Usage: Normal<br>
        • Cache Status: Active
        """
        create_info_card(performance_content, "info")

if __name__ == "__main__":
    main()
