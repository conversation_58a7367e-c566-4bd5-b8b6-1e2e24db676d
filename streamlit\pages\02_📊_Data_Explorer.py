"""
Data Explorer Page for GoFood Sentiment Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import APP_CONFIG
from components.styling import load_css, create_header, create_info_card
from utils.data_loader import load_raw_data, load_preprocessed_data, filter_data, add_sentiment_labels
from utils.visualization import (
    create_sentiment_distribution_chart,
    create_rating_distribution_chart,
    create_text_length_distribution,
    create_wordcloud
)
from utils.preprocessing import get_text_statistics

def main():
    """Data Explorer main function"""
    
    # Configure page
    st.set_page_config(
        page_title="Data Explorer - GoFood Sentiment Analysis",
        page_icon="📊",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="📊 Data Explorer",
        subtitle="Eksplorasi dan Analisis Data GoFood Reviews"
    )
    
    # Load data
    with st.spinner("🔄 Loading data..."):
        df_raw = load_raw_data()
        df_processed = load_preprocessed_data()
    
    if df_raw is None:
        st.error("❌ Failed to load data")
        st.stop()
    
    # Add sentiment labels
    df_with_sentiment = add_sentiment_labels(df_raw)
    
    # Sidebar filters
    st.sidebar.header("🔍 Data Filters")
    
    # Rating filter
    if 'rating' in df_raw.columns:
        rating_options = sorted(df_raw['rating'].dropna().unique())
        selected_ratings = st.sidebar.multiselect(
            "Filter by Rating:",
            options=rating_options,
            default=rating_options,
            help="Select ratings to include in analysis"
        )
    else:
        selected_ratings = []
    
    # Sentiment filter
    if 'sentiment_label' in df_with_sentiment.columns:
        sentiment_options = df_with_sentiment['sentiment_label'].dropna().unique()
        selected_sentiments = st.sidebar.multiselect(
            "Filter by Sentiment:",
            options=sentiment_options,
            default=list(sentiment_options),
            help="Select sentiments to include in analysis"
        )
    else:
        selected_sentiments = []
    
    # Text length filter
    st.sidebar.subheader("📝 Text Length Filter")
    if 'ulasan' in df_raw.columns:
        text_lengths = df_raw['ulasan'].astype(str).str.len()
        min_length = st.sidebar.slider(
            "Minimum text length:",
            min_value=0,
            max_value=int(text_lengths.max()),
            value=0,
            help="Minimum number of characters"
        )
        max_length = st.sidebar.slider(
            "Maximum text length:",
            min_value=min_length,
            max_value=int(text_lengths.max()),
            value=int(text_lengths.max()),
            help="Maximum number of characters"
        )
    else:
        min_length = 0
        max_length = 1000
    
    # Apply filters
    filters = {
        'rating': selected_ratings,
        'sentiment': selected_sentiments,
        'min_length': min_length,
        'max_length': max_length
    }
    
    filtered_df = filter_data(df_with_sentiment, filters)
    
    # Display filter results
    st.subheader("📋 Filtered Dataset Overview")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📝 Total Records",
            value=f"{len(filtered_df):,}",
            delta=f"{len(filtered_df) - len(df_raw):+,}"
        )
    
    with col2:
        if 'rating' in filtered_df.columns and not filtered_df.empty:
            avg_rating = filtered_df['rating'].mean()
            st.metric(
                label="⭐ Average Rating",
                value=f"{avg_rating:.2f}"
            )
        else:
            st.metric(label="⭐ Average Rating", value="N/A")
    
    with col3:
        if 'sentiment_label' in filtered_df.columns and not filtered_df.empty:
            positive_pct = (filtered_df['sentiment_label'] == 'Positif').mean() * 100
            st.metric(
                label="😊 Positive %",
                value=f"{positive_pct:.1f}%"
            )
        else:
            st.metric(label="😊 Positive %", value="N/A")
    
    with col4:
        if 'ulasan' in filtered_df.columns and not filtered_df.empty:
            avg_length = filtered_df['ulasan'].astype(str).str.len().mean()
            st.metric(
                label="📏 Avg Text Length",
                value=f"{avg_length:.0f}"
            )
        else:
            st.metric(label="📏 Avg Text Length", value="N/A")
    
    if filtered_df.empty:
        st.warning("⚠️ No data matches the selected filters. Please adjust your filter criteria.")
        st.stop()
    
    st.markdown("---")
    
    # Visualization Section
    st.subheader("📈 Data Visualizations")
    
    # Create tabs for different visualizations
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Distributions", "📝 Text Analysis", "🔍 Detailed View", "📋 Statistics"])
    
    with tab1:
        col1, col2 = st.columns(2)
        
        with col1:
            # Rating distribution
            if 'rating' in filtered_df.columns:
                fig_rating = create_rating_distribution_chart(filtered_df, 'rating')
                st.plotly_chart(fig_rating, use_container_width=True)
            else:
                st.info("Rating data not available")
        
        with col2:
            # Sentiment distribution
            if 'sentiment_label' in filtered_df.columns:
                chart_type = st.selectbox(
                    "Chart Type:",
                    options=['pie', 'bar', 'donut'],
                    index=0,
                    key="sentiment_chart_type"
                )
                fig_sentiment = create_sentiment_distribution_chart(
                    filtered_df, 'sentiment_label', chart_type
                )
                st.plotly_chart(fig_sentiment, use_container_width=True)
            else:
                st.info("Sentiment data not available")
        
        # Text length distribution
        if 'ulasan' in filtered_df.columns:
            fig_text_length = create_text_length_distribution(filtered_df, 'ulasan')
            st.plotly_chart(fig_text_length, use_container_width=True)
    
    with tab2:
        st.subheader("📝 Text Analysis")
        
        if 'ulasan' in filtered_df.columns:
            # Word cloud section
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**☁️ Word Cloud - All Reviews**")
                
                # Create word cloud for all reviews
                texts = filtered_df['ulasan'].astype(str).tolist()
                wordcloud = create_wordcloud(texts, max_words=100)
                
                if wordcloud:
                    import matplotlib.pyplot as plt
                    fig, ax = plt.subplots(figsize=(10, 5))
                    ax.imshow(wordcloud, interpolation='bilinear')
                    ax.axis('off')
                    st.pyplot(fig)
                else:
                    st.info("Unable to generate word cloud")
            
            with col2:
                st.markdown("**☁️ Word Cloud - By Sentiment**")
                
                if 'sentiment_label' in filtered_df.columns:
                    sentiment_choice = st.selectbox(
                        "Select sentiment:",
                        options=filtered_df['sentiment_label'].unique(),
                        key="wordcloud_sentiment"
                    )
                    
                    sentiment_texts = filtered_df[
                        filtered_df['sentiment_label'] == sentiment_choice
                    ]['ulasan'].astype(str).tolist()
                    
                    wordcloud_sentiment = create_wordcloud(sentiment_texts, max_words=100)
                    
                    if wordcloud_sentiment:
                        fig, ax = plt.subplots(figsize=(10, 5))
                        ax.imshow(wordcloud_sentiment, interpolation='bilinear')
                        ax.axis('off')
                        st.pyplot(fig)
                    else:
                        st.info("Unable to generate word cloud for selected sentiment")
                else:
                    st.info("Sentiment labels not available")
            
            # Text statistics
            st.markdown("**📊 Text Statistics**")
            
            if not filtered_df.empty:
                sample_text = filtered_df['ulasan'].iloc[0] if len(filtered_df) > 0 else ""
                text_stats = get_text_statistics(sample_text)
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    avg_chars = filtered_df['ulasan'].astype(str).str.len().mean()
                    st.metric("Avg Characters", f"{avg_chars:.0f}")
                
                with col2:
                    avg_words = filtered_df['ulasan'].astype(str).str.split().str.len().mean()
                    st.metric("Avg Words", f"{avg_words:.0f}")
                
                with col3:
                    max_chars = filtered_df['ulasan'].astype(str).str.len().max()
                    st.metric("Max Characters", f"{max_chars}")
                
                with col4:
                    min_chars = filtered_df['ulasan'].astype(str).str.len().min()
                    st.metric("Min Characters", f"{min_chars}")
        else:
            st.info("Text column not found for analysis")
    
    with tab3:
        st.subheader("🔍 Detailed Data View")
        
        # Search functionality
        search_term = st.text_input(
            "🔍 Search in reviews:",
            placeholder="Enter keywords to search...",
            help="Search for specific terms in the review text"
        )
        
        # Display data
        display_df = filtered_df.copy()
        
        if search_term and 'ulasan' in display_df.columns:
            mask = display_df['ulasan'].astype(str).str.contains(
                search_term, case=False, na=False
            )
            display_df = display_df[mask]
            st.info(f"Found {len(display_df)} reviews containing '{search_term}'")
        
        # Select columns to display
        available_columns = list(display_df.columns)
        default_columns = ['rating', 'ulasan', 'sentiment_label'] if all(
            col in available_columns for col in ['rating', 'ulasan', 'sentiment_label']
        ) else available_columns[:3]
        
        selected_columns = st.multiselect(
            "Select columns to display:",
            options=available_columns,
            default=default_columns
        )
        
        if selected_columns:
            # Pagination
            page_size = st.selectbox("Rows per page:", [10, 25, 50, 100], index=1)
            total_pages = (len(display_df) - 1) // page_size + 1
            
            if total_pages > 1:
                page_number = st.selectbox(
                    f"Page (1-{total_pages}):",
                    range(1, total_pages + 1)
                )
                start_idx = (page_number - 1) * page_size
                end_idx = start_idx + page_size
                page_df = display_df[selected_columns].iloc[start_idx:end_idx]
            else:
                page_df = display_df[selected_columns]
            
            st.dataframe(
                page_df,
                use_container_width=True,
                hide_index=True
            )
            
            # Download button
            csv = display_df[selected_columns].to_csv(index=False)
            st.download_button(
                label="📥 Download filtered data as CSV",
                data=csv,
                file_name="gofood_filtered_data.csv",
                mime="text/csv"
            )
        else:
            st.warning("Please select at least one column to display")
    
    with tab4:
        st.subheader("📋 Statistical Summary")
        
        # Descriptive statistics
        if not filtered_df.empty:
            st.markdown("**📊 Descriptive Statistics**")
            
            # Numeric columns
            numeric_cols = filtered_df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                st.dataframe(
                    filtered_df[numeric_cols].describe(),
                    use_container_width=True
                )
            else:
                st.info("No numeric columns found for statistical analysis")
            
            # Categorical analysis
            st.markdown("**📈 Categorical Analysis**")
            
            categorical_cols = filtered_df.select_dtypes(include=['object']).columns
            
            for col in categorical_cols[:3]:  # Limit to first 3 categorical columns
                if col in filtered_df.columns:
                    st.markdown(f"**{col.title()} Distribution:**")
                    value_counts = filtered_df[col].value_counts()
                    
                    col1, col2 = st.columns([2, 1])
                    
                    with col1:
                        st.bar_chart(value_counts)
                    
                    with col2:
                        st.dataframe(
                            pd.DataFrame({
                                'Value': value_counts.index,
                                'Count': value_counts.values,
                                'Percentage': (value_counts.values / len(filtered_df) * 100).round(2)
                            }),
                            hide_index=True
                        )
        else:
            st.info("No data available for statistical analysis")

if __name__ == "__main__":
    main()
