"""
Model Comparison Page for GoFood Sentiment Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import APP_CONFIG
from components.styling import load_css, create_header, create_info_card, create_metric_card
from utils.model_utils import (
    load_all_models,
    get_model_info,
    compare_models,
    get_confusion_matrix,
    get_feature_importance,
    load_vectorizer
)
from utils.data_loader import load_preprocessed_data
from utils.visualization import (
    create_model_comparison_chart,
    create_confusion_matrix_heatmap,
    create_feature_importance_chart,
    create_metrics_dashboard
)

def main():
    """Model comparison main function"""
    
    # Configure page
    st.set_page_config(
        page_title="Model Comparison - GoFood Sentiment Analysis",
        page_icon="🤖",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="🤖 Model Comparison",
        subtitle="Perbandingan Performa Model Machine Learning"
    )
    
    # Load models
    with st.spinner("🔄 Loading models..."):
        models = load_all_models()
        vectorizer = load_vectorizer()
    
    if not models:
        st.error("❌ No models available for comparison. Please ensure model files exist.")
        st.info("Expected model files: model_random_forest.pkl, model_logistic_regression.pkl, best_model.pkl")
        st.stop()
    
    # Model overview
    st.subheader("📋 Available Models")
    
    model_cols = st.columns(len(models))
    
    for i, (model_key, model) in enumerate(models.items()):
        with model_cols[i]:
            model_info = get_model_info(model_key)
            
            model_name = model_info.get('name', model_key.replace('_', ' ').title())
            model_type = model_info.get('type', 'Unknown')
            
            info_content = f"""
            <strong>Model:</strong> {model_name}<br>
            <strong>Type:</strong> {model_type}<br>
            <strong>Status:</strong> ✅ Loaded
            """
            
            create_info_card(info_content, "success")
    
    st.markdown("---")
    
    # Create tabs for different comparison aspects
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Performance Metrics", "🎯 Confusion Matrix", "🔍 Feature Analysis", "📈 Detailed Comparison"])
    
    with tab1:
        show_performance_metrics(models)
    
    with tab2:
        show_confusion_matrices(models)
    
    with tab3:
        show_feature_analysis(models, vectorizer)
    
    with tab4:
        show_detailed_comparison(models)

def show_performance_metrics(models):
    """Show performance metrics comparison"""
    
    st.subheader("📊 Performance Metrics Comparison")
    
    # Note about test data
    st.info("ℹ️ Performance metrics require test data. This is a demonstration with placeholder data.")
    
    # Placeholder metrics (in real implementation, these would come from actual model evaluation)
    placeholder_metrics = {
        'random_forest': {
            'accuracy': 0.85,
            'precision': 0.83,
            'recall': 0.87,
            'f1_score': 0.85
        },
        'logistic_regression': {
            'accuracy': 0.82,
            'precision': 0.80,
            'recall': 0.84,
            'f1_score': 0.82
        },
        'best_model': {
            'accuracy': 0.88,
            'precision': 0.86,
            'recall': 0.90,
            'f1_score': 0.88
        }
    }
    
    # Create metrics dataframe
    metrics_data = []
    for model_key in models.keys():
        if model_key in placeholder_metrics:
            metrics = placeholder_metrics[model_key]
            metrics['model'] = model_key.replace('_', ' ').title()
            metrics_data.append(metrics)
    
    if metrics_data:
        df_metrics = pd.DataFrame(metrics_data)
        
        # Display metrics table
        st.markdown("**📋 Metrics Table:**")
        st.dataframe(
            df_metrics.round(4),
            use_container_width=True,
            hide_index=True
        )
        
        # Metrics visualization
        st.markdown("**📈 Metrics Visualization:**")
        fig_comparison = create_model_comparison_chart(df_metrics)
        st.plotly_chart(fig_comparison, use_container_width=True)
        
        # Best model highlight
        best_model_idx = df_metrics['accuracy'].idxmax()
        best_model = df_metrics.iloc[best_model_idx]
        
        st.markdown("**🏆 Best Performing Model:**")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="🤖 Model",
                value=best_model['model']
            )
        
        with col2:
            st.metric(
                label="🎯 Accuracy",
                value=f"{best_model['accuracy']:.2%}"
            )
        
        with col3:
            st.metric(
                label="📊 Precision",
                value=f"{best_model['precision']:.2%}"
            )
        
        with col4:
            st.metric(
                label="🔄 Recall",
                value=f"{best_model['recall']:.2%}"
            )
        
        # Metrics dashboard
        st.markdown("**📊 Metrics Dashboard:**")
        best_metrics = {
            'accuracy': best_model['accuracy'],
            'precision': best_model['precision'],
            'recall': best_model['recall'],
            'f1_score': best_model['f1_score']
        }
        
        fig_dashboard = create_metrics_dashboard(best_metrics)
        st.plotly_chart(fig_dashboard, use_container_width=True)
    
    else:
        st.warning("⚠️ No metrics data available for comparison")

def show_confusion_matrices(models):
    """Show confusion matrices for models"""
    
    st.subheader("🎯 Confusion Matrix Analysis")
    
    st.info("ℹ️ Confusion matrices require test data. This is a demonstration with placeholder data.")
    
    # Model selection for confusion matrix
    selected_model = st.selectbox(
        "Select model for confusion matrix:",
        options=list(models.keys()),
        format_func=lambda x: x.replace('_', ' ').title()
    )
    
    # Placeholder confusion matrix data
    placeholder_cm = np.array([
        [150, 20, 5],
        [25, 180, 15],
        [10, 30, 165]
    ])
    
    labels = ['Negatif', 'Netral', 'Positif']
    
    # Display confusion matrix
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown(f"**🎯 Confusion Matrix - {selected_model.replace('_', ' ').title()}:**")
        
        fig_cm = create_confusion_matrix_heatmap(placeholder_cm, labels)
        st.plotly_chart(fig_cm, use_container_width=True)
    
    with col2:
        st.markdown("**📊 Classification Report:**")
        
        # Calculate metrics from confusion matrix
        total = placeholder_cm.sum()
        accuracy = np.trace(placeholder_cm) / total
        
        # Per-class metrics
        precision = np.diag(placeholder_cm) / placeholder_cm.sum(axis=0)
        recall = np.diag(placeholder_cm) / placeholder_cm.sum(axis=1)
        f1 = 2 * (precision * recall) / (precision + recall)
        
        metrics_df = pd.DataFrame({
            'Class': labels,
            'Precision': precision,
            'Recall': recall,
            'F1-Score': f1
        })
        
        st.dataframe(
            metrics_df.round(3),
            use_container_width=True,
            hide_index=True
        )
        
        st.metric("Overall Accuracy", f"{accuracy:.2%}")
    
    # Model comparison for confusion matrices
    st.markdown("**🔄 Compare All Models:**")
    
    if st.button("Generate Comparison Report"):
        st.markdown("**📋 Confusion Matrix Comparison Report:**")
        
        for model_key in models.keys():
            with st.expander(f"📊 {model_key.replace('_', ' ').title()}", expanded=False):
                # Use same placeholder data for demonstration
                fig_cm = create_confusion_matrix_heatmap(placeholder_cm, labels)
                st.plotly_chart(fig_cm, use_container_width=True)

def show_feature_analysis(models, vectorizer):
    """Show feature importance analysis"""
    
    st.subheader("🔍 Feature Importance Analysis")
    
    if vectorizer is None:
        st.warning("⚠️ Vectorizer not available. Feature analysis requires the TF-IDF vectorizer.")
        return
    
    # Model selection for feature analysis
    selected_model = st.selectbox(
        "Select model for feature analysis:",
        options=list(models.keys()),
        format_func=lambda x: x.replace('_', ' ').title(),
        key="feature_model_select"
    )
    
    model = models[selected_model]
    
    # Get feature names from vectorizer
    try:
        feature_names = vectorizer.get_feature_names_out()
    except:
        feature_names = [f"feature_{i}" for i in range(100)]  # Placeholder
    
    # Get feature importance
    feature_importance = get_feature_importance(model, feature_names, top_n=20)
    
    if feature_importance:
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.markdown(f"**📊 Top Features - {selected_model.replace('_', ' ').title()}:**")
            
            fig_importance = create_feature_importance_chart(feature_importance, top_n=20)
            st.plotly_chart(fig_importance, use_container_width=True)
        
        with col2:
            st.markdown("**📋 Feature Importance Table:**")
            
            importance_df = pd.DataFrame([
                {'Feature': k, 'Importance': v}
                for k, v in list(feature_importance.items())[:10]
            ])
            
            st.dataframe(
                importance_df.round(4),
                use_container_width=True,
                hide_index=True
            )
            
            # Feature statistics
            st.markdown("**📊 Feature Statistics:**")
            st.metric("Total Features", len(feature_names))
            st.metric("Top Features Shown", len(feature_importance))
            st.metric("Max Importance", f"{max(feature_importance.values()):.4f}")
    
    else:
        st.info("ℹ️ Feature importance not available for this model type.")
    
    # Feature comparison across models
    st.markdown("**🔄 Feature Comparison Across Models:**")
    
    if st.button("Compare Feature Importance"):
        comparison_data = []
        
        for model_key, model in models.items():
            importance = get_feature_importance(model, feature_names, top_n=10)
            if importance:
                for feature, score in importance.items():
                    comparison_data.append({
                        'Model': model_key.replace('_', ' ').title(),
                        'Feature': feature,
                        'Importance': score
                    })
        
        if comparison_data:
            comparison_df = pd.DataFrame(comparison_data)
            
            # Pivot table for comparison
            pivot_df = comparison_df.pivot(index='Feature', columns='Model', values='Importance')
            pivot_df = pivot_df.fillna(0)
            
            st.dataframe(
                pivot_df.round(4),
                use_container_width=True
            )
            
            # Heatmap visualization
            st.markdown("**🔥 Feature Importance Heatmap:**")
            st.bar_chart(pivot_df.T)
        else:
            st.info("No feature importance data available for comparison.")

def show_detailed_comparison(models):
    """Show detailed model comparison"""
    
    st.subheader("📈 Detailed Model Analysis")
    
    # Model information table
    st.markdown("**🤖 Model Information:**")
    
    model_info_data = []
    for model_key, model in models.items():
        info = get_model_info(model_key)
        
        model_info_data.append({
            'Model': info.get('name', model_key.replace('_', ' ').title()),
            'Type': info.get('type', 'Unknown'),
            'Parameters': len(info.get('parameters', {})),
            'Status': '✅ Loaded'
        })
    
    info_df = pd.DataFrame(model_info_data)
    st.dataframe(info_df, use_container_width=True, hide_index=True)
    
    # Model parameters comparison
    st.markdown("**⚙️ Model Parameters:**")
    
    selected_models = st.multiselect(
        "Select models to compare parameters:",
        options=list(models.keys()),
        default=list(models.keys())[:2],
        format_func=lambda x: x.replace('_', ' ').title()
    )
    
    if selected_models:
        params_data = []
        
        for model_key in selected_models:
            info = get_model_info(model_key)
            params = info.get('parameters', {})
            
            for param, value in params.items():
                params_data.append({
                    'Model': model_key.replace('_', ' ').title(),
                    'Parameter': param,
                    'Value': str(value)
                })
        
        if params_data:
            params_df = pd.DataFrame(params_data)
            
            # Pivot for better comparison
            params_pivot = params_df.pivot(index='Parameter', columns='Model', values='Value')
            st.dataframe(params_pivot, use_container_width=True)
        else:
            st.info("No parameter information available for selected models.")
    
    # Model recommendations
    st.markdown("**💡 Model Recommendations:**")
    
    recommendations = {
        'Random Forest': {
            'pros': ['Good for non-linear relationships', 'Handles missing values well', 'Provides feature importance'],
            'cons': ['Can overfit with small datasets', 'Less interpretable than linear models'],
            'use_case': 'Best for complex patterns and when interpretability is not critical'
        },
        'Logistic Regression': {
            'pros': ['Highly interpretable', 'Fast training and prediction', 'Good baseline model'],
            'cons': ['Assumes linear relationship', 'Sensitive to outliers'],
            'use_case': 'Best when interpretability is important and relationships are linear'
        },
        'Best Model': {
            'pros': ['Optimized performance', 'Validated on test data', 'Production ready'],
            'cons': ['May be more complex', 'Requires more computational resources'],
            'use_case': 'Best for production deployment when accuracy is priority'
        }
    }
    
    for model_name, info in recommendations.items():
        with st.expander(f"📊 {model_name} Analysis", expanded=False):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("**✅ Pros:**")
                for pro in info['pros']:
                    st.write(f"• {pro}")
            
            with col2:
                st.markdown("**❌ Cons:**")
                for con in info['cons']:
                    st.write(f"• {con}")
            
            with col3:
                st.markdown("**🎯 Use Case:**")
                st.write(info['use_case'])

if __name__ == "__main__":
    main()
