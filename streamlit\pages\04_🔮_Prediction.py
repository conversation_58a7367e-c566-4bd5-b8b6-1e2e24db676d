"""
Prediction Interface Page for GoFood Sentiment Analysis
"""

import streamlit as st
import pandas as pd
import sys
from pathlib import Path
import io

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import APP_CONFIG, TEXT_CONFIG
from components.styling import load_css, create_header, create_info_card, create_alert
from utils.model_utils import predict_sentiment, predict_batch, load_all_models
from utils.preprocessing import preprocess_text, show_preprocessing_steps
from utils.visualization import create_prediction_confidence_chart

def main():
    """Prediction interface main function"""
    
    # Configure page
    st.set_page_config(
        page_title="Prediction - GoFood Sentiment Analysis",
        page_icon="🔮",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="🔮 Sentiment Prediction",
        subtitle="Prediksi Sentimen Real-time untuk Review GoFood"
    )
    
    # Load models
    with st.spinner("🔄 Loading models..."):
        models = load_all_models()
    
    if not models:
        st.error("❌ No models available for prediction. Please ensure model files exist.")
        st.info("Expected model files: model_random_forest.pkl, model_logistic_regression.pkl, best_model.pkl")
        st.stop()
    
    # Model selection
    st.sidebar.header("🤖 Model Settings")
    
    available_models = list(models.keys())
    selected_model = st.sidebar.selectbox(
        "Select Model:",
        options=available_models,
        index=0 if 'best_model' not in available_models else available_models.index('best_model'),
        help="Choose which model to use for prediction"
    )
    
    # Prediction mode
    prediction_mode = st.sidebar.radio(
        "Prediction Mode:",
        options=["Single Text", "Batch Upload"],
        help="Choose between single text prediction or batch file upload"
    )
    
    # Show preprocessing options
    st.sidebar.subheader("🔧 Preprocessing Options")
    show_preprocessing = st.sidebar.checkbox(
        "Show preprocessing steps",
        value=True,
        help="Display text preprocessing steps"
    )
    
    show_confidence = st.sidebar.checkbox(
        "Show confidence scores",
        value=True,
        help="Display prediction confidence scores"
    )
    
    st.markdown("---")
    
    if prediction_mode == "Single Text":
        show_single_prediction(selected_model, show_preprocessing, show_confidence)
    else:
        show_batch_prediction(selected_model, show_preprocessing, show_confidence)

def show_single_prediction(model_name: str, show_preprocessing: bool, show_confidence: bool):
    """Show single text prediction interface"""
    
    st.subheader("📝 Single Text Prediction")
    
    # Text input
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # Sample texts for quick testing
        sample_texts = [
            "Aplikasi sangat bagus dan mudah digunakan!",
            "Pelayanan buruk, makanan terlambat datang",
            "Biasa saja, tidak ada yang istimewa",
            "Sangat puas dengan layanan GoFood, recommended!",
            "Aplikasi sering error dan lambat"
        ]
        
        selected_sample = st.selectbox(
            "📋 Quick Test (Optional):",
            options=[""] + sample_texts,
            index=0,
            help="Select a sample text for quick testing"
        )
        
        # Text area for input
        input_text = st.text_area(
            "✍️ Enter review text:",
            value=selected_sample,
            height=150,
            max_chars=TEXT_CONFIG['max_text_length'],
            placeholder="Masukkan teks review yang ingin dianalisis...",
            help=f"Maximum {TEXT_CONFIG['max_text_length']} characters"
        )
        
        # Predict button
        predict_button = st.button(
            "🔮 Predict Sentiment",
            type="primary",
            disabled=len(input_text.strip()) < TEXT_CONFIG['min_text_length']
        )
    
    with col2:
        # Text statistics
        if input_text:
            st.markdown("**📊 Text Statistics**")
            
            char_count = len(input_text)
            word_count = len(input_text.split())
            
            st.metric("Characters", char_count)
            st.metric("Words", word_count)
            
            # Validation
            if char_count < TEXT_CONFIG['min_text_length']:
                create_alert(
                    f"Text too short (minimum {TEXT_CONFIG['min_text_length']} characters)",
                    "warning"
                )
            elif char_count > TEXT_CONFIG['max_text_length']:
                create_alert(
                    f"Text too long (maximum {TEXT_CONFIG['max_text_length']} characters)",
                    "warning"
                )
            else:
                create_alert("Text length is valid", "success")
    
    # Show preprocessing steps
    if show_preprocessing and input_text:
        st.markdown("---")
        st.subheader("🔧 Text Preprocessing Steps")
        
        preprocessing_steps = show_preprocessing_steps(input_text)
        
        with st.expander("👁️ View Preprocessing Steps", expanded=False):
            for step_name, step_text in preprocessing_steps.items():
                st.markdown(f"**{step_name.title()}:**")
                st.code(step_text[:200] + "..." if len(step_text) > 200 else step_text)
    
    # Prediction results
    if predict_button and input_text.strip():
        st.markdown("---")
        st.subheader("🎯 Prediction Results")
        
        with st.spinner("🔄 Analyzing sentiment..."):
            result = predict_sentiment(input_text, model_name)
        
        if result.get('error'):
            st.error(f"❌ Prediction failed: {result['error']}")
        else:
            # Display prediction
            prediction = result.get('prediction', 'Unknown')
            confidence = result.get('confidence')
            probabilities = result.get('probabilities', {})
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                # Sentiment result
                sentiment_color = {
                    'Positif': 'success',
                    'Negatif': 'danger',
                    'Netral': 'warning'
                }.get(prediction, 'info')
                
                sentiment_emoji = {
                    'Positif': '😊',
                    'Negatif': '😞',
                    'Netral': '😐'
                }.get(prediction, '🤔')
                
                st.markdown(f"**🎯 Predicted Sentiment:**")
                create_alert(
                    f"{sentiment_emoji} **{prediction}**",
                    sentiment_color
                )
            
            with col2:
                # Confidence score
                if show_confidence and confidence is not None:
                    st.markdown(f"**📊 Confidence Score:**")
                    st.metric(
                        label="Confidence",
                        value=f"{confidence:.2%}",
                        help="Model confidence in the prediction"
                    )
                    
                    # Confidence bar
                    st.progress(confidence)
            
            with col3:
                # Model used
                st.markdown(f"**🤖 Model Used:**")
                st.info(f"Model: {model_name.replace('_', ' ').title()}")
            
            # Probability distribution
            if show_confidence and probabilities:
                st.markdown("**📈 Probability Distribution:**")
                
                prob_df = pd.DataFrame([
                    {'Sentiment': k, 'Probability': v}
                    for k, v in probabilities.items()
                ])
                
                st.bar_chart(
                    prob_df.set_index('Sentiment')['Probability'],
                    height=300
                )
            
            # Export results
            st.markdown("---")
            st.markdown("**💾 Export Results**")
            
            result_data = {
                'Input Text': input_text,
                'Predicted Sentiment': prediction,
                'Confidence': confidence if confidence else 'N/A',
                'Model Used': model_name,
                'Processed Text': result.get('processed_text', '')
            }
            
            result_df = pd.DataFrame([result_data])
            csv = result_df.to_csv(index=False)
            
            st.download_button(
                label="📥 Download Result as CSV",
                data=csv,
                file_name=f"sentiment_prediction_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

def show_batch_prediction(model_name: str, show_preprocessing: bool, show_confidence: bool):
    """Show batch prediction interface"""
    
    st.subheader("📁 Batch Prediction")
    
    # File upload
    uploaded_file = st.file_uploader(
        "📤 Upload CSV file:",
        type=['csv'],
        help="Upload a CSV file with a column containing review texts"
    )
    
    if uploaded_file is not None:
        try:
            # Read uploaded file
            df = pd.read_csv(uploaded_file)
            
            st.success(f"✅ File uploaded successfully! Found {len(df)} rows.")
            
            # Column selection
            text_columns = df.select_dtypes(include=['object']).columns.tolist()
            
            if not text_columns:
                st.error("❌ No text columns found in the uploaded file.")
                return
            
            selected_column = st.selectbox(
                "📝 Select text column:",
                options=text_columns,
                help="Choose the column containing review texts"
            )
            
            # Preview data
            st.markdown("**👁️ Data Preview:**")
            st.dataframe(df.head(), use_container_width=True)
            
            # Batch size setting
            batch_size = st.slider(
                "Batch size:",
                min_value=10,
                max_value=min(1000, len(df)),
                value=min(100, len(df)),
                help="Number of texts to process at once"
            )
            
            # Process button
            if st.button("🔮 Process Batch Prediction", type="primary"):
                
                # Validate text column
                if selected_column not in df.columns:
                    st.error("❌ Selected column not found in the data.")
                    return
                
                # Get texts
                texts = df[selected_column].astype(str).tolist()
                
                # Filter out empty texts
                valid_texts = [text for text in texts if len(text.strip()) >= TEXT_CONFIG['min_text_length']]
                
                if len(valid_texts) != len(texts):
                    st.warning(f"⚠️ Filtered out {len(texts) - len(valid_texts)} texts that were too short.")
                
                if not valid_texts:
                    st.error("❌ No valid texts found for prediction.")
                    return
                
                # Process in batches
                st.markdown("---")
                st.subheader("🔄 Processing Results")
                
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                all_results = []
                
                for i in range(0, len(valid_texts), batch_size):
                    batch_texts = valid_texts[i:i + batch_size]
                    
                    status_text.text(f"Processing batch {i//batch_size + 1}/{(len(valid_texts)-1)//batch_size + 1}...")
                    
                    # Predict batch
                    batch_results = predict_batch(batch_texts, model_name)
                    all_results.extend(batch_results)
                    
                    # Update progress
                    progress = min((i + batch_size) / len(valid_texts), 1.0)
                    progress_bar.progress(progress)
                
                status_text.text("✅ Processing complete!")
                
                # Create results dataframe
                results_df = pd.DataFrame(all_results)
                
                # Add original data
                if len(results_df) == len(df):
                    for col in df.columns:
                        if col != selected_column:
                            results_df[f'original_{col}'] = df[col].values
                
                # Display results summary
                st.markdown("**📊 Results Summary:**")
                
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Processed", len(results_df))
                
                with col2:
                    if 'prediction' in results_df.columns:
                        positive_count = (results_df['prediction'] == 'Positif').sum()
                        st.metric("Positive", positive_count)
                
                with col3:
                    if 'prediction' in results_df.columns:
                        negative_count = (results_df['prediction'] == 'Negatif').sum()
                        st.metric("Negative", negative_count)
                
                with col4:
                    if 'confidence' in results_df.columns:
                        avg_confidence = results_df['confidence'].mean()
                        st.metric("Avg Confidence", f"{avg_confidence:.2%}")
                
                # Visualization
                if show_confidence and 'prediction' in results_df.columns:
                    st.markdown("**📈 Results Visualization:**")
                    
                    # Sentiment distribution
                    sentiment_counts = results_df['prediction'].value_counts()
                    st.bar_chart(sentiment_counts)
                    
                    # Confidence distribution
                    if 'confidence' in results_df.columns:
                        fig_confidence = create_prediction_confidence_chart(all_results)
                        st.plotly_chart(fig_confidence, use_container_width=True)
                
                # Display results table
                st.markdown("**📋 Detailed Results:**")
                
                # Select columns to display
                display_columns = st.multiselect(
                    "Select columns to display:",
                    options=results_df.columns.tolist(),
                    default=['original_text', 'prediction', 'confidence'][:len(results_df.columns)]
                )
                
                if display_columns:
                    st.dataframe(
                        results_df[display_columns],
                        use_container_width=True,
                        hide_index=True
                    )
                
                # Download results
                st.markdown("**💾 Download Results:**")
                
                csv_results = results_df.to_csv(index=False)
                
                st.download_button(
                    label="📥 Download Results as CSV",
                    data=csv_results,
                    file_name=f"batch_sentiment_results_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
                
        except Exception as e:
            st.error(f"❌ Error processing file: {str(e)}")
    
    else:
        # Show sample CSV format
        st.markdown("**📋 Sample CSV Format:**")
        
        sample_data = pd.DataFrame({
            'review_text': [
                'Aplikasi sangat bagus dan mudah digunakan!',
                'Pelayanan buruk, makanan terlambat datang',
                'Biasa saja, tidak ada yang istimewa'
            ],
            'rating': [5, 1, 3]
        })
        
        st.dataframe(sample_data, use_container_width=True, hide_index=True)
        
        # Download sample
        sample_csv = sample_data.to_csv(index=False)
        st.download_button(
            label="📥 Download Sample CSV",
            data=sample_csv,
            file_name="sample_reviews.csv",
            mime="text/csv"
        )

if __name__ == "__main__":
    main()
