"""
Advanced Analytics Page for GoFood Sentiment Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import APP_CONFIG
from components.styling import load_css, create_header, create_info_card, create_metric_card
from utils.data_loader import load_raw_data, load_preprocessed_data, add_sentiment_labels
from utils.visualization import (
    create_sentiment_distribution_chart,
    create_sentiment_trend_chart,
    create_wordcloud
)

def main():
    """Advanced Analytics main function"""
    
    # Configure page
    st.set_page_config(
        page_title="Analytics - GoFood Sentiment Analysis",
        page_icon="📈",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="📈 Advanced Analytics",
        subtitle="Analisis Mendalam dan Business Insights"
    )
    
    # Load data
    with st.spinner("🔄 Loading data..."):
        df_raw = load_raw_data()
        df_processed = load_preprocessed_data()
    
    if df_raw is None:
        st.error("❌ Failed to load data")
        st.stop()
    
    # Add sentiment labels
    df_with_sentiment = add_sentiment_labels(df_raw)
    
    # Create tabs for different analytics
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Business Insights", "📈 Trend Analysis", "🔍 Topic Analysis", "💡 Recommendations"])
    
    with tab1:
        show_business_insights(df_with_sentiment)
    
    with tab2:
        show_trend_analysis(df_with_sentiment)
    
    with tab3:
        show_topic_analysis(df_with_sentiment, df_processed)
    
    with tab4:
        show_recommendations(df_with_sentiment)

def show_business_insights(df):
    """Show business insights and KPIs"""
    
    st.subheader("📊 Business Intelligence Dashboard")
    
    if df.empty:
        st.warning("No data available for analysis")
        return
    
    # Key Business Metrics
    st.markdown("**🎯 Key Performance Indicators**")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        # Customer Satisfaction Score
        if 'rating' in df.columns:
            avg_rating = df['rating'].mean()
            satisfaction_score = (avg_rating / 5) * 100
            
            st.metric(
                label="😊 Customer Satisfaction",
                value=f"{satisfaction_score:.1f}%",
                delta=f"{avg_rating:.2f}/5.0",
                help="Based on average rating"
            )
        else:
            st.metric(label="😊 Customer Satisfaction", value="N/A")
    
    with col2:
        # Positive Sentiment Rate
        if 'sentiment_label' in df.columns:
            positive_rate = (df['sentiment_label'] == 'Positif').mean() * 100
            
            st.metric(
                label="👍 Positive Sentiment",
                value=f"{positive_rate:.1f}%",
                help="Percentage of positive reviews"
            )
        else:
            st.metric(label="👍 Positive Sentiment", value="N/A")
    
    with col3:
        # Negative Sentiment Rate
        if 'sentiment_label' in df.columns:
            negative_rate = (df['sentiment_label'] == 'Negatif').mean() * 100
            
            st.metric(
                label="👎 Negative Sentiment",
                value=f"{negative_rate:.1f}%",
                delta=f"-{negative_rate:.1f}%",
                delta_color="inverse",
                help="Percentage of negative reviews"
            )
        else:
            st.metric(label="👎 Negative Sentiment", value="N/A")
    
    with col4:
        # Review Volume
        total_reviews = len(df)
        st.metric(
            label="📝 Total Reviews",
            value=f"{total_reviews:,}",
            help="Total number of reviews analyzed"
        )
    
    st.markdown("---")
    
    # Detailed Analysis
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**📈 Rating Distribution Analysis**")
        
        if 'rating' in df.columns:
            rating_dist = df['rating'].value_counts().sort_index()
            
            # Calculate percentages
            rating_pct = (rating_dist / len(df) * 100).round(1)
            
            # Create analysis
            analysis_data = []
            for rating, count in rating_dist.items():
                pct = rating_pct[rating]
                analysis_data.append({
                    'Rating': f"{rating} ⭐",
                    'Count': count,
                    'Percentage': f"{pct}%"
                })
            
            analysis_df = pd.DataFrame(analysis_data)
            st.dataframe(analysis_df, use_container_width=True, hide_index=True)
            
            # Key insights
            most_common_rating = rating_dist.idxmax()
            st.info(f"💡 Most common rating: {most_common_rating} stars ({rating_pct[most_common_rating]}%)")
            
        else:
            st.info("Rating data not available")
    
    with col2:
        st.markdown("**🎭 Sentiment Analysis Summary**")
        
        if 'sentiment_label' in df.columns:
            sentiment_dist = df['sentiment_label'].value_counts()
            sentiment_pct = (sentiment_dist / len(df) * 100).round(1)
            
            # Create sentiment analysis
            sentiment_data = []
            for sentiment, count in sentiment_dist.items():
                pct = sentiment_pct[sentiment]
                emoji = {'Positif': '😊', 'Negatif': '😞', 'Netral': '😐'}.get(sentiment, '🤔')
                sentiment_data.append({
                    'Sentiment': f"{emoji} {sentiment}",
                    'Count': count,
                    'Percentage': f"{pct}%"
                })
            
            sentiment_df = pd.DataFrame(sentiment_data)
            st.dataframe(sentiment_df, use_container_width=True, hide_index=True)
            
            # Sentiment insights
            dominant_sentiment = sentiment_dist.idxmax()
            st.info(f"💡 Dominant sentiment: {dominant_sentiment} ({sentiment_pct[dominant_sentiment]}%)")
            
        else:
            st.info("Sentiment data not available")
    
    # Business Impact Analysis
    st.markdown("**💼 Business Impact Analysis**")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Customer Retention Risk
        if 'sentiment_label' in df.columns:
            negative_pct = (df['sentiment_label'] == 'Negatif').mean() * 100
            
            if negative_pct > 30:
                risk_level = "🔴 High Risk"
                risk_color = "danger"
            elif negative_pct > 15:
                risk_level = "🟡 Medium Risk"
                risk_color = "warning"
            else:
                risk_level = "🟢 Low Risk"
                risk_color = "success"
            
            content = f"""
            <strong>Customer Retention Risk:</strong><br>
            {risk_level}<br>
            <strong>Negative Reviews:</strong> {negative_pct:.1f}%
            """
            
            create_info_card(content, risk_color)
    
    with col2:
        # App Store Rating Impact
        if 'rating' in df.columns:
            avg_rating = df['rating'].mean()
            
            if avg_rating >= 4.5:
                rating_impact = "🌟 Excellent"
                impact_color = "success"
            elif avg_rating >= 4.0:
                rating_impact = "👍 Good"
                impact_color = "success"
            elif avg_rating >= 3.5:
                rating_impact = "⚠️ Needs Improvement"
                impact_color = "warning"
            else:
                rating_impact = "🚨 Critical"
                impact_color = "danger"
            
            content = f"""
            <strong>App Store Impact:</strong><br>
            {rating_impact}<br>
            <strong>Average Rating:</strong> {avg_rating:.2f}/5.0
            """
            
            create_info_card(content, impact_color)
    
    with col3:
        # Review Volume Trend
        review_volume = len(df)
        
        if review_volume > 1000:
            volume_status = "📈 High Engagement"
            volume_color = "success"
        elif review_volume > 500:
            volume_status = "📊 Moderate Engagement"
            volume_color = "info"
        else:
            volume_status = "📉 Low Engagement"
            volume_color = "warning"
        
        content = f"""
        <strong>User Engagement:</strong><br>
        {volume_status}<br>
        <strong>Total Reviews:</strong> {review_volume:,}
        """
        
        create_info_card(content, volume_color)

def show_trend_analysis(df):
    """Show trend analysis over time"""
    
    st.subheader("📈 Trend Analysis")
    
    st.info("📅 Trend analysis requires date information. This is a demonstration with simulated data.")
    
    # Simulate date data for demonstration
    if not df.empty:
        # Create simulated dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=len(df))
        
        date_range = pd.date_range(start=start_date, end=end_date, periods=len(df))
        df_trend = df.copy()
        df_trend['date'] = date_range
        
        # Time period selection
        time_period = st.selectbox(
            "📅 Select time period:",
            options=["Last 7 days", "Last 30 days", "Last 90 days", "All time"],
            index=2
        )
        
        # Filter data based on time period
        if time_period == "Last 7 days":
            cutoff_date = end_date - timedelta(days=7)
        elif time_period == "Last 30 days":
            cutoff_date = end_date - timedelta(days=30)
        elif time_period == "Last 90 days":
            cutoff_date = end_date - timedelta(days=90)
        else:
            cutoff_date = start_date
        
        filtered_df = df_trend[df_trend['date'] >= cutoff_date]
        
        # Trend visualization
        if 'sentiment_label' in filtered_df.columns:
            fig_trend = create_sentiment_trend_chart(filtered_df, 'date', 'sentiment_label')
            st.plotly_chart(fig_trend, use_container_width=True)
        
        # Trend insights
        st.markdown("**📊 Trend Insights**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Daily average metrics
            if 'rating' in filtered_df.columns:
                daily_avg = filtered_df.groupby(filtered_df['date'].dt.date)['rating'].mean()
                
                st.markdown("**📈 Daily Rating Trends:**")
                st.line_chart(daily_avg)
                
                # Trend direction
                if len(daily_avg) > 1:
                    trend_direction = "📈 Improving" if daily_avg.iloc[-1] > daily_avg.iloc[0] else "📉 Declining"
                    st.info(f"Trend: {trend_direction}")
        
        with col2:
            # Volume trends
            daily_volume = filtered_df.groupby(filtered_df['date'].dt.date).size()
            
            st.markdown("**📊 Daily Review Volume:**")
            st.bar_chart(daily_volume)
            
            # Volume insights
            avg_daily_volume = daily_volume.mean()
            st.info(f"Average daily reviews: {avg_daily_volume:.1f}")
    
    else:
        st.warning("No data available for trend analysis")

def show_topic_analysis(df, df_processed):
    """Show topic analysis and word clouds"""
    
    st.subheader("🔍 Topic Analysis")
    
    if df.empty:
        st.warning("No data available for topic analysis")
        return
    
    # Word cloud analysis
    st.markdown("**☁️ Word Cloud Analysis**")
    
    if 'ulasan' in df.columns:
        # Overall word cloud
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**All Reviews**")
            
            texts = df['ulasan'].astype(str).tolist()
            wordcloud = create_wordcloud(texts, max_words=100)
            
            if wordcloud:
                import matplotlib.pyplot as plt
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.imshow(wordcloud, interpolation='bilinear')
                ax.axis('off')
                st.pyplot(fig)
            else:
                st.info("Unable to generate word cloud")
        
        with col2:
            st.markdown("**By Sentiment**")
            
            if 'sentiment_label' in df.columns:
                sentiment_filter = st.selectbox(
                    "Select sentiment:",
                    options=df['sentiment_label'].unique(),
                    key="topic_sentiment"
                )
                
                sentiment_texts = df[df['sentiment_label'] == sentiment_filter]['ulasan'].astype(str).tolist()
                sentiment_wordcloud = create_wordcloud(sentiment_texts, max_words=100)
                
                if sentiment_wordcloud:
                    fig, ax = plt.subplots(figsize=(10, 6))
                    ax.imshow(sentiment_wordcloud, interpolation='bilinear')
                    ax.axis('off')
                    st.pyplot(fig)
                else:
                    st.info("Unable to generate word cloud for selected sentiment")
    
    # Topic insights
    st.markdown("**💡 Topic Insights**")
    
    # Common themes analysis
    if 'ulasan' in df.columns:
        # Simple keyword analysis
        all_text = ' '.join(df['ulasan'].astype(str).tolist()).lower()
        
        # Common positive keywords
        positive_keywords = ['bagus', 'baik', 'cepat', 'mudah', 'puas', 'recommended', 'mantap', 'oke']
        negative_keywords = ['buruk', 'lambat', 'error', 'susah', 'jelek', 'kecewa', 'mahal', 'lama']
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**😊 Positive Themes:**")
            positive_counts = {}
            for keyword in positive_keywords:
                count = all_text.count(keyword)
                if count > 0:
                    positive_counts[keyword] = count
            
            if positive_counts:
                positive_df = pd.DataFrame([
                    {'Theme': k.title(), 'Mentions': v}
                    for k, v in sorted(positive_counts.items(), key=lambda x: x[1], reverse=True)
                ])
                st.dataframe(positive_df, use_container_width=True, hide_index=True)
            else:
                st.info("No positive themes detected")
        
        with col2:
            st.markdown("**😞 Negative Themes:**")
            negative_counts = {}
            for keyword in negative_keywords:
                count = all_text.count(keyword)
                if count > 0:
                    negative_counts[keyword] = count
            
            if negative_counts:
                negative_df = pd.DataFrame([
                    {'Theme': k.title(), 'Mentions': v}
                    for k, v in sorted(negative_counts.items(), key=lambda x: x[1], reverse=True)
                ])
                st.dataframe(negative_df, use_container_width=True, hide_index=True)
            else:
                st.info("No negative themes detected")

def show_recommendations(df):
    """Show business recommendations based on analysis"""
    
    st.subheader("💡 Business Recommendations")
    
    if df.empty:
        st.warning("No data available for recommendations")
        return
    
    # Generate recommendations based on data analysis
    recommendations = []
    
    # Rating-based recommendations
    if 'rating' in df.columns:
        avg_rating = df['rating'].mean()
        low_ratings = (df['rating'] <= 2).sum()
        low_rating_pct = (low_ratings / len(df)) * 100
        
        if avg_rating < 3.5:
            recommendations.append({
                'priority': 'High',
                'category': 'Customer Satisfaction',
                'issue': f'Low average rating ({avg_rating:.2f}/5.0)',
                'recommendation': 'Focus on improving core app functionality and user experience',
                'impact': 'Critical for app store ranking and user acquisition'
            })
        
        if low_rating_pct > 20:
            recommendations.append({
                'priority': 'High',
                'category': 'Quality Control',
                'issue': f'High percentage of 1-2 star reviews ({low_rating_pct:.1f}%)',
                'recommendation': 'Implement immediate quality assurance measures and bug fixes',
                'impact': 'Reduces negative word-of-mouth and improves retention'
            })
    
    # Sentiment-based recommendations
    if 'sentiment_label' in df.columns:
        negative_pct = (df['sentiment_label'] == 'Negatif').mean() * 100
        positive_pct = (df['sentiment_label'] == 'Positif').mean() * 100
        
        if negative_pct > 25:
            recommendations.append({
                'priority': 'Medium',
                'category': 'Customer Experience',
                'issue': f'High negative sentiment ({negative_pct:.1f}%)',
                'recommendation': 'Analyze negative feedback patterns and address common complaints',
                'impact': 'Improves customer satisfaction and reduces churn'
            })
        
        if positive_pct > 60:
            recommendations.append({
                'priority': 'Low',
                'category': 'Marketing',
                'issue': f'Strong positive sentiment ({positive_pct:.1f}%)',
                'recommendation': 'Leverage positive reviews for marketing campaigns and testimonials',
                'impact': 'Enhances brand reputation and attracts new users'
            })
    
    # Volume-based recommendations
    review_volume = len(df)
    if review_volume < 500:
        recommendations.append({
            'priority': 'Medium',
            'category': 'User Engagement',
            'issue': f'Low review volume ({review_volume} reviews)',
            'recommendation': 'Implement review incentives and improve user engagement strategies',
            'impact': 'Increases user feedback and improves product insights'
        })
    
    # Display recommendations
    if recommendations:
        # Priority filter
        priority_filter = st.selectbox(
            "Filter by priority:",
            options=['All', 'High', 'Medium', 'Low'],
            index=0
        )
        
        filtered_recommendations = recommendations
        if priority_filter != 'All':
            filtered_recommendations = [r for r in recommendations if r['priority'] == priority_filter]
        
        # Display recommendations
        for i, rec in enumerate(filtered_recommendations):
            priority_color = {
                'High': 'danger',
                'Medium': 'warning',
                'Low': 'info'
            }.get(rec['priority'], 'info')
            
            with st.expander(f"🎯 {rec['category']} - {rec['priority']} Priority", expanded=True):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    st.markdown(f"**🔍 Issue:** {rec['issue']}")
                    st.markdown(f"**💡 Recommendation:** {rec['recommendation']}")
                    st.markdown(f"**📈 Expected Impact:** {rec['impact']}")
                
                with col2:
                    create_info_card(
                        f"<strong>Priority:</strong> {rec['priority']}<br><strong>Category:</strong> {rec['category']}",
                        priority_color
                    )
        
        # Action plan
        st.markdown("---")
        st.markdown("**📋 Action Plan Summary**")
        
        high_priority = [r for r in recommendations if r['priority'] == 'High']
        medium_priority = [r for r in recommendations if r['priority'] == 'Medium']
        low_priority = [r for r in recommendations if r['priority'] == 'Low']
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("🔴 High Priority", len(high_priority))
            if high_priority:
                st.write("**Immediate Actions:**")
                for rec in high_priority:
                    st.write(f"• {rec['category']}")
        
        with col2:
            st.metric("🟡 Medium Priority", len(medium_priority))
            if medium_priority:
                st.write("**Short-term Goals:**")
                for rec in medium_priority:
                    st.write(f"• {rec['category']}")
        
        with col3:
            st.metric("🟢 Low Priority", len(low_priority))
            if low_priority:
                st.write("**Long-term Strategy:**")
                for rec in low_priority:
                    st.write(f"• {rec['category']}")
    
    else:
        st.success("🎉 Great job! No critical issues detected in the current data.")
        st.info("💡 Continue monitoring user feedback and maintaining high service quality.")

if __name__ == "__main__":
    main()
