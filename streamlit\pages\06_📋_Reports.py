"""
Reports Page for GoFood Sentiment Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import sys
from pathlib import Path
from datetime import datetime
import io
import base64

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from config.settings import APP_CONFIG
from components.styling import load_css, create_header, create_info_card
from utils.data_loader import load_raw_data, load_preprocessed_data, add_sentiment_labels, get_data_summary
from utils.model_utils import load_all_models

def main():
    """Reports main function"""
    
    # Configure page
    st.set_page_config(
        page_title="Reports - GoFood Sentiment Analysis",
        page_icon="📋",
        layout="wide"
    )
    
    # Load CSS
    load_css()
    
    # Header
    create_header(
        title="📋 Reports & Export",
        subtitle="Generate dan Export Laporan Analisis Sentimen"
    )
    
    # Load data
    with st.spinner("🔄 Loading data..."):
        df_raw = load_raw_data()
        df_processed = load_preprocessed_data()
        models = load_all_models()
    
    if df_raw is None:
        st.error("❌ Failed to load data")
        st.stop()
    
    # Add sentiment labels
    df_with_sentiment = add_sentiment_labels(df_raw)
    
    # Report type selection
    st.subheader("📊 Report Types")
    
    report_type = st.selectbox(
        "Select report type:",
        options=[
            "Executive Summary",
            "Technical Analysis Report",
            "Data Quality Report",
            "Model Performance Report",
            "Custom Report"
        ],
        help="Choose the type of report to generate"
    )
    
    # Report configuration
    st.sidebar.header("⚙️ Report Configuration")
    
    include_charts = st.sidebar.checkbox("Include visualizations", value=True)
    include_raw_data = st.sidebar.checkbox("Include raw data", value=False)
    include_recommendations = st.sidebar.checkbox("Include recommendations", value=True)
    
    # Date range for report
    st.sidebar.subheader("📅 Report Period")
    report_period = st.sidebar.selectbox(
        "Select period:",
        options=["All time", "Last 30 days", "Last 90 days", "Custom range"]
    )
    
    # Generate report based on type
    st.markdown("---")
    
    if report_type == "Executive Summary":
        generate_executive_summary(df_with_sentiment, include_charts, include_recommendations)
    elif report_type == "Technical Analysis Report":
        generate_technical_report(df_with_sentiment, df_processed, include_charts)
    elif report_type == "Data Quality Report":
        generate_data_quality_report(df_raw, df_processed)
    elif report_type == "Model Performance Report":
        generate_model_performance_report(models, include_charts)
    elif report_type == "Custom Report":
        generate_custom_report(df_with_sentiment, df_processed, models)

def generate_executive_summary(df, include_charts, include_recommendations):
    """Generate executive summary report"""
    
    st.subheader("📊 Executive Summary Report")
    
    # Report metadata
    report_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Key metrics
    total_reviews = len(df)
    avg_rating = df['rating'].mean() if 'rating' in df.columns else 0
    
    if 'sentiment_label' in df.columns:
        positive_pct = (df['sentiment_label'] == 'Positif').mean() * 100
        negative_pct = (df['sentiment_label'] == 'Negatif').mean() * 100
        neutral_pct = (df['sentiment_label'] == 'Netral').mean() * 100
    else:
        positive_pct = negative_pct = neutral_pct = 0
    
    # Generate report content
    report_content = f"""
# GoFood Merchant Sentiment Analysis - Executive Summary

**Report Generated:** {report_date}
**Analysis Period:** All available data
**Total Reviews Analyzed:** {total_reviews:,}

## Key Findings

### Overall Performance
- **Average Rating:** {avg_rating:.2f}/5.0
- **Customer Satisfaction Score:** {(avg_rating/5)*100:.1f}%
- **Positive Sentiment:** {positive_pct:.1f}%
- **Negative Sentiment:** {negative_pct:.1f}%
- **Neutral Sentiment:** {neutral_pct:.1f}%

### Business Impact
- **Customer Retention Risk:** {'High' if negative_pct > 30 else 'Medium' if negative_pct > 15 else 'Low'}
- **App Store Rating Impact:** {'Excellent' if avg_rating >= 4.5 else 'Good' if avg_rating >= 4.0 else 'Needs Improvement'}
- **User Engagement Level:** {'High' if total_reviews > 1000 else 'Medium' if total_reviews > 500 else 'Low'}

### Key Insights
1. **Sentiment Distribution:** {positive_pct:.1f}% positive, {negative_pct:.1f}% negative, {neutral_pct:.1f}% neutral
2. **Rating Concentration:** Most reviews are {df['rating'].mode().iloc[0] if 'rating' in df.columns else 'N/A'} stars
3. **Review Volume:** {total_reviews:,} total reviews analyzed

## Recommendations
"""
    
    if include_recommendations:
        if negative_pct > 25:
            report_content += "\n- **Priority 1:** Address negative feedback patterns to improve customer satisfaction"
        if avg_rating < 4.0:
            report_content += "\n- **Priority 2:** Focus on improving core app functionality and user experience"
        if total_reviews < 500:
            report_content += "\n- **Priority 3:** Implement strategies to increase user engagement and feedback"
        
        report_content += "\n- **Opportunity:** Leverage positive reviews for marketing and testimonials"
    
    report_content += f"""

## Data Quality
- **Completeness:** {((len(df) * len(df.columns) - df.isnull().sum().sum()) / (len(df) * len(df.columns)) * 100):.1f}%
- **Data Sources:** GoFood Merchant reviews from Google Play Store
- **Processing Status:** {'✅ Processed' if df is not None else '❌ Pending'}

---
*This report was automatically generated by the GoFood Sentiment Analysis Dashboard*
"""
    
    # Display report
    st.markdown(report_content)
    
    # Visualizations
    if include_charts and 'sentiment_label' in df.columns:
        st.markdown("### Visualizations")
        
        from streamlit.utils.visualization import create_sentiment_distribution_chart, create_rating_distribution_chart
        
        col1, col2 = st.columns(2)
        
        with col1:
            fig_sentiment = create_sentiment_distribution_chart(df, 'sentiment_label')
            st.plotly_chart(fig_sentiment, use_container_width=True)
        
        with col2:
            if 'rating' in df.columns:
                fig_rating = create_rating_distribution_chart(df, 'rating')
                st.plotly_chart(fig_rating, use_container_width=True)
    
    # Export options
    st.markdown("### 💾 Export Options")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Export as text
        st.download_button(
            label="📄 Download as Text",
            data=report_content,
            file_name=f"executive_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain"
        )
    
    with col2:
        # Export as CSV (summary data)
        summary_data = {
            'Metric': ['Total Reviews', 'Average Rating', 'Positive %', 'Negative %', 'Neutral %'],
            'Value': [total_reviews, f"{avg_rating:.2f}", f"{positive_pct:.1f}%", f"{negative_pct:.1f}%", f"{neutral_pct:.1f}%"]
        }
        summary_df = pd.DataFrame(summary_data)
        csv_data = summary_df.to_csv(index=False)
        
        st.download_button(
            label="📊 Download Summary CSV",
            data=csv_data,
            file_name=f"summary_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
    
    with col3:
        # Export full data
        if st.button("📥 Export Full Dataset"):
            csv_full = df.to_csv(index=False)
            st.download_button(
                label="📋 Download Full Data",
                data=csv_full,
                file_name=f"full_dataset_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

def generate_technical_report(df, df_processed, include_charts):
    """Generate technical analysis report"""
    
    st.subheader("🔬 Technical Analysis Report")
    
    # Data processing statistics
    st.markdown("### 📊 Data Processing Statistics")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Raw Data:**")
        raw_stats = {
            'Total Records': len(df),
            'Columns': len(df.columns),
            'Missing Values': df.isnull().sum().sum(),
            'Data Types': len(df.dtypes.unique())
        }
        
        for key, value in raw_stats.items():
            st.metric(key, value)
    
    with col2:
        if df_processed is not None:
            st.markdown("**Processed Data:**")
            processed_stats = {
                'Processed Records': len(df_processed),
                'Processing Success Rate': f"{(len(df_processed)/len(df)*100):.1f}%",
                'Text Columns': len(df_processed.select_dtypes(include=['object']).columns),
                'Numeric Columns': len(df_processed.select_dtypes(include=[np.number]).columns)
            }
            
            for key, value in processed_stats.items():
                st.metric(key, value)
        else:
            st.info("Processed data not available")
    
    # Data quality analysis
    st.markdown("### 🔍 Data Quality Analysis")
    
    quality_issues = []
    
    # Check for missing values
    missing_pct = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
    if missing_pct > 5:
        quality_issues.append(f"High missing value rate: {missing_pct:.1f}%")
    
    # Check for duplicates
    duplicate_count = df.duplicated().sum()
    if duplicate_count > 0:
        quality_issues.append(f"Duplicate records found: {duplicate_count}")
    
    # Check text length distribution
    if 'ulasan' in df.columns:
        text_lengths = df['ulasan'].astype(str).str.len()
        very_short = (text_lengths < 10).sum()
        very_long = (text_lengths > 1000).sum()
        
        if very_short > len(df) * 0.1:
            quality_issues.append(f"Many very short reviews: {very_short} ({very_short/len(df)*100:.1f}%)")
        
        if very_long > 0:
            quality_issues.append(f"Very long reviews detected: {very_long}")
    
    if quality_issues:
        st.warning("⚠️ Data Quality Issues Detected:")
        for issue in quality_issues:
            st.write(f"• {issue}")
    else:
        st.success("✅ No major data quality issues detected")
    
    # Technical metrics
    st.markdown("### 📈 Technical Metrics")
    
    if include_charts:
        # Text length distribution
        if 'ulasan' in df.columns:
            from streamlit.utils.visualization import create_text_length_distribution
            fig_length = create_text_length_distribution(df, 'ulasan')
            st.plotly_chart(fig_length, use_container_width=True)
    
    # Export technical report
    technical_data = {
        'Raw Records': len(df),
        'Processed Records': len(df_processed) if df_processed is not None else 0,
        'Missing Value %': missing_pct,
        'Duplicate Records': duplicate_count,
        'Quality Score': max(0, 100 - missing_pct - (duplicate_count/len(df)*100))
    }
    
    tech_df = pd.DataFrame([technical_data])
    csv_tech = tech_df.to_csv(index=False)
    
    st.download_button(
        label="📊 Download Technical Report",
        data=csv_tech,
        file_name=f"technical_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv"
    )

def generate_data_quality_report(df_raw, df_processed):
    """Generate data quality report"""
    
    st.subheader("🔍 Data Quality Report")
    
    # Comprehensive data quality analysis
    summary = get_data_summary(df_raw)
    
    st.markdown("### 📊 Data Quality Metrics")
    
    # Quality score calculation
    total_cells = len(df_raw) * len(df_raw.columns)
    missing_cells = df_raw.isnull().sum().sum()
    completeness_score = ((total_cells - missing_cells) / total_cells) * 100
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Completeness", f"{completeness_score:.1f}%")
    
    with col2:
        duplicate_rate = (df_raw.duplicated().sum() / len(df_raw)) * 100
        st.metric("Uniqueness", f"{100-duplicate_rate:.1f}%")
    
    with col3:
        # Consistency check (placeholder)
        consistency_score = 95.0  # Placeholder
        st.metric("Consistency", f"{consistency_score:.1f}%")
    
    with col4:
        # Overall quality score
        overall_quality = (completeness_score + (100-duplicate_rate) + consistency_score) / 3
        st.metric("Overall Quality", f"{overall_quality:.1f}%")
    
    # Detailed quality analysis
    st.markdown("### 📋 Detailed Analysis")
    
    # Missing values by column
    missing_by_column = df_raw.isnull().sum()
    missing_by_column = missing_by_column[missing_by_column > 0]
    
    if not missing_by_column.empty:
        st.markdown("**Missing Values by Column:**")
        missing_df = pd.DataFrame({
            'Column': missing_by_column.index,
            'Missing Count': missing_by_column.values,
            'Missing %': (missing_by_column.values / len(df_raw) * 100).round(2)
        })
        st.dataframe(missing_df, use_container_width=True, hide_index=True)
    else:
        st.success("✅ No missing values detected")
    
    # Export quality report
    quality_report_data = {
        'Metric': ['Completeness', 'Uniqueness', 'Consistency', 'Overall Quality'],
        'Score': [completeness_score, 100-duplicate_rate, consistency_score, overall_quality]
    }
    
    quality_df = pd.DataFrame(quality_report_data)
    csv_quality = quality_df.to_csv(index=False)
    
    st.download_button(
        label="📊 Download Quality Report",
        data=csv_quality,
        file_name=f"data_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv"
    )

def generate_model_performance_report(models, include_charts):
    """Generate model performance report"""
    
    st.subheader("🤖 Model Performance Report")
    
    if not models:
        st.warning("No models available for performance reporting")
        return
    
    # Model overview
    st.markdown("### 📊 Model Overview")
    
    model_data = []
    for model_key, model in models.items():
        model_data.append({
            'Model': model_key.replace('_', ' ').title(),
            'Type': type(model).__name__,
            'Status': '✅ Loaded',
            'Parameters': len(getattr(model, 'get_params', lambda: {})())
        })
    
    model_df = pd.DataFrame(model_data)
    st.dataframe(model_df, use_container_width=True, hide_index=True)
    
    # Placeholder performance metrics
    st.markdown("### 📈 Performance Metrics")
    st.info("ℹ️ Performance metrics require test data. Showing placeholder data for demonstration.")
    
    # Placeholder metrics
    performance_data = {
        'Model': ['Random Forest', 'Logistic Regression', 'Best Model'],
        'Accuracy': [0.85, 0.82, 0.88],
        'Precision': [0.83, 0.80, 0.86],
        'Recall': [0.87, 0.84, 0.90],
        'F1-Score': [0.85, 0.82, 0.88]
    }
    
    perf_df = pd.DataFrame(performance_data)
    st.dataframe(perf_df, use_container_width=True, hide_index=True)
    
    if include_charts:
        # Performance comparison chart
        from streamlit.utils.visualization import create_model_comparison_chart
        fig_comparison = create_model_comparison_chart(perf_df)
        st.plotly_chart(fig_comparison, use_container_width=True)
    
    # Export model report
    csv_models = perf_df.to_csv(index=False)
    
    st.download_button(
        label="📊 Download Model Report",
        data=csv_models,
        file_name=f"model_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv"
    )

def generate_custom_report(df, df_processed, models):
    """Generate custom report with user-selected components"""
    
    st.subheader("📝 Custom Report Builder")
    
    # Report components selection
    st.markdown("### 🔧 Select Report Components")
    
    col1, col2 = st.columns(2)
    
    with col1:
        include_overview = st.checkbox("📊 Data Overview", value=True)
        include_sentiment = st.checkbox("🎭 Sentiment Analysis", value=True)
        include_ratings = st.checkbox("⭐ Rating Analysis", value=True)
        include_quality = st.checkbox("🔍 Data Quality", value=False)
    
    with col2:
        include_models = st.checkbox("🤖 Model Information", value=False)
        include_charts = st.checkbox("📈 Visualizations", value=True)
        include_recommendations = st.checkbox("💡 Recommendations", value=True)
        include_raw_data = st.checkbox("📋 Raw Data Sample", value=False)
    
    # Generate custom report
    if st.button("🔄 Generate Custom Report", type="primary"):
        
        report_content = f"""
# GoFood Sentiment Analysis - Custom Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Report Type:** Custom Analysis Report

"""
        
        if include_overview:
            report_content += f"""
## 📊 Data Overview
- **Total Reviews:** {len(df):,}
- **Data Columns:** {len(df.columns)}
- **Data Quality Score:** {((len(df) * len(df.columns) - df.isnull().sum().sum()) / (len(df) * len(df.columns)) * 100):.1f}%

"""
        
        if include_sentiment and 'sentiment_label' in df.columns:
            sentiment_dist = df['sentiment_label'].value_counts()
            sentiment_pct = (sentiment_dist / len(df) * 100).round(1)
            
            report_content += f"""
## 🎭 Sentiment Analysis
- **Positive:** {sentiment_pct.get('Positif', 0):.1f}% ({sentiment_dist.get('Positif', 0):,} reviews)
- **Negative:** {sentiment_pct.get('Negatif', 0):.1f}% ({sentiment_dist.get('Negatif', 0):,} reviews)
- **Neutral:** {sentiment_pct.get('Netral', 0):.1f}% ({sentiment_dist.get('Netral', 0):,} reviews)

"""
        
        if include_ratings and 'rating' in df.columns:
            avg_rating = df['rating'].mean()
            rating_dist = df['rating'].value_counts().sort_index()
            
            report_content += f"""
## ⭐ Rating Analysis
- **Average Rating:** {avg_rating:.2f}/5.0
- **Rating Distribution:**
"""
            for rating, count in rating_dist.items():
                pct = (count / len(df)) * 100
                report_content += f"  - {rating} stars: {count:,} reviews ({pct:.1f}%)\n"
            
            report_content += "\n"
        
        if include_models and models:
            report_content += f"""
## 🤖 Model Information
- **Available Models:** {len(models)}
- **Model Types:** {', '.join([type(model).__name__ for model in models.values()])}

"""
        
        # Display the custom report
        st.markdown("### 📄 Generated Report")
        st.markdown(report_content)
        
        # Export custom report
        st.download_button(
            label="📥 Download Custom Report",
            data=report_content,
            file_name=f"custom_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain"
        )
        
        # Include raw data if requested
        if include_raw_data:
            st.markdown("### 📋 Raw Data Sample")
            st.dataframe(df.head(100), use_container_width=True)
            
            csv_sample = df.head(100).to_csv(index=False)
            st.download_button(
                label="📊 Download Data Sample",
                data=csv_sample,
                file_name=f"data_sample_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

if __name__ == "__main__":
    main()
