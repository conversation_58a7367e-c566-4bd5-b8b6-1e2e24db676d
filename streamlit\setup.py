#!/usr/bin/env python3
"""
Setup script for GoFood Sentiment Analysis Dashboard
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_directory_structure():
    """Create necessary directories"""
    base_dir = Path(__file__).parent
    
    directories = [
        "data/cache",
        "assets",
        "logs",
        "exports"
    ]
    
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_environment():
    """Setup Python environment"""
    print("🐍 Setting up Python environment...")
    
    # Check if virtual environment exists
    venv_path = Path(__file__).parent / "venv"
    
    if not venv_path.exists():
        create_venv = input("📦 Create virtual environment? (recommended) (y/n): ").lower().strip()
        
        if create_venv in ['y', 'yes']:
            try:
                subprocess.check_call([sys.executable, "-m", "venv", str(venv_path)])
                print("✅ Virtual environment created")
                
                # Activate virtual environment instructions
                if os.name == 'nt':  # Windows
                    activate_script = venv_path / "Scripts" / "activate.bat"
                    print(f"📝 To activate: {activate_script}")
                else:  # Unix/Linux/macOS
                    activate_script = venv_path / "bin" / "activate"
                    print(f"📝 To activate: source {activate_script}")
                    
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to create virtual environment: {e}")
    else:
        print("✅ Virtual environment already exists")

def install_dependencies():
    """Install required dependencies"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        print("📦 Installing dependencies...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ])
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_data_files():
    """Check and setup data files"""
    print("📁 Checking data files...")
    
    parent_dir = Path(__file__).parent.parent
    streamlit_dir = Path(__file__).parent
    
    # Required data files
    data_files = {
        "reviews_gofood_Merchant.xlsx": "Main dataset",
        "model_random_forest.pkl": "Random Forest model",
        "model_logistic_regression.pkl": "Logistic Regression model", 
        "best_model.pkl": "Best performing model",
        "tfidf_vectorizer_tuned.pkl": "TF-IDF vectorizer"
    }
    
    missing_files = []
    
    for file_name, description in data_files.items():
        file_path = parent_dir / file_name
        
        if file_path.exists():
            print(f"✅ {file_name} - {description}")
        else:
            missing_files.append((file_name, description))
            print(f"❌ {file_name} - {description}")
    
    if missing_files:
        print(f"\n⚠️ Missing {len(missing_files)} required files:")
        for file_name, description in missing_files:
            print(f"   • {file_name} - {description}")
        
        print("\n📝 To get these files:")
        print("   1. Run the main analysis notebooks to generate models")
        print("   2. Ensure the Excel data file is in the parent directory")
        print("   3. Check the main project README for data sources")
        
        return False
    
    print("✅ All required data files found")
    return True

def create_config_files():
    """Create configuration files"""
    print("⚙️ Creating configuration files...")
    
    streamlit_dir = Path(__file__).parent
    
    # Create .streamlit directory
    streamlit_config_dir = streamlit_dir / ".streamlit"
    streamlit_config_dir.mkdir(exist_ok=True)
    
    # Create config.toml
    config_content = """
[global]
developmentMode = false

[server]
port = 8501
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f8f9fa"
textColor = "#212529"
"""
    
    config_file = streamlit_config_dir / "config.toml"
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    print("✅ Streamlit configuration created")
    
    # Create credentials.toml (if needed)
    credentials_content = """
[general]
email = ""
"""
    
    credentials_file = streamlit_config_dir / "credentials.toml"
    if not credentials_file.exists():
        with open(credentials_file, 'w') as f:
            f.write(credentials_content)
        print("✅ Credentials file created")

def create_launch_scripts():
    """Create platform-specific launch scripts"""
    print("🚀 Creating launch scripts...")
    
    streamlit_dir = Path(__file__).parent
    
    # Windows batch script
    windows_script = streamlit_dir / "launch.bat"
    windows_content = """@echo off
echo Starting GoFood Sentiment Analysis Dashboard...
python launch.py
pause
"""
    
    with open(windows_script, 'w') as f:
        f.write(windows_content)
    
    # Unix/Linux shell script
    unix_script = streamlit_dir / "launch.sh"
    unix_content = """#!/bin/bash
echo "Starting GoFood Sentiment Analysis Dashboard..."
python3 launch.py
"""
    
    with open(unix_script, 'w') as f:
        f.write(unix_content)
    
    # Make shell script executable
    try:
        os.chmod(unix_script, 0o755)
    except:
        pass  # Windows doesn't support chmod
    
    print("✅ Launch scripts created")
    print(f"   • Windows: {windows_script}")
    print(f"   • Unix/Linux: {unix_script}")

def run_tests():
    """Run basic tests"""
    print("🧪 Running basic tests...")
    
    try:
        # Test imports
        import streamlit
        import pandas
        import numpy
        import plotly
        print("✅ Core packages import successfully")
        
        # Test app file
        app_file = Path(__file__).parent / "app.py"
        if app_file.exists():
            print("✅ Main app file exists")
        else:
            print("❌ Main app file missing")
            return False
        
        # Test configuration
        config_dir = Path(__file__).parent / "config"
        if config_dir.exists():
            print("✅ Configuration directory exists")
        else:
            print("❌ Configuration directory missing")
            return False
        
        print("✅ Basic tests passed")
        return True
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 GoFood Sentiment Analysis Dashboard Setup")
    print("=" * 50)
    
    # Create directory structure
    print("\n📁 Creating directory structure...")
    create_directory_structure()
    
    # Setup environment
    print("\n🐍 Setting up environment...")
    setup_environment()
    
    # Install dependencies
    print("\n📦 Installing dependencies...")
    if not install_dependencies():
        print("❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Check data files
    print("\n📊 Checking data files...")
    data_files_ok = check_data_files()
    
    # Create config files
    print("\n⚙️ Creating configuration...")
    create_config_files()
    
    # Create launch scripts
    print("\n🚀 Creating launch scripts...")
    create_launch_scripts()
    
    # Run tests
    print("\n🧪 Running tests...")
    if not run_tests():
        print("⚠️ Some tests failed, but setup can continue")
    
    # Final summary
    print("\n" + "=" * 50)
    print("✅ Setup completed!")
    
    if data_files_ok:
        print("\n🎉 Dashboard is ready to launch!")
        print("🚀 Run: python launch.py")
    else:
        print("\n⚠️ Dashboard setup complete, but missing data files")
        print("📝 Please ensure all required data files are available")
        print("🚀 You can still run: python launch.py")
    
    print("\n📚 Documentation:")
    print("   • README.md - Complete documentation")
    print("   • DEVELOPMENT_PLAN.md - Development details")
    
    print("\n🔗 Quick start:")
    print("   1. Ensure data files are in parent directory")
    print("   2. Run: python launch.py")
    print("   3. Open browser to http://localhost:8501")
    
    print("\n💡 Need help?")
    print("   • Check README.md for troubleshooting")
    print("   • Verify all data files exist")
    print("   • Ensure Python 3.8+ is installed")

if __name__ == "__main__":
    main()
