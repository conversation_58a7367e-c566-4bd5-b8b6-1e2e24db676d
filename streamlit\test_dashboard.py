#!/usr/bin/env python3
"""
Test script for GoFood Sentiment Analysis Dashboard
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import importlib.util

# Add the parent directory to the path
sys.path.append(str(Path(__file__).parent.parent))

def test_imports():
    """Test if all required packages can be imported"""
    print("🧪 Testing package imports...")
    
    required_packages = [
        'streamlit',
        'pandas', 
        'numpy',
        'plotly',
        'matplotlib',
        'seaborn',
        'sklearn',
        'joblib'
    ]
    
    failed_imports = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError as e:
            failed_imports.append((package, str(e)))
            print(f"❌ {package}: {e}")
    
    return len(failed_imports) == 0, failed_imports

def test_file_structure():
    """Test if all required files exist"""
    print("\n📁 Testing file structure...")
    
    base_dir = Path(__file__).parent
    
    required_files = [
        "app.py",
        "requirements.txt",
        "README.md",
        "config/settings.py",
        "config/constants.py",
        "utils/data_loader.py",
        "utils/model_utils.py",
        "utils/preprocessing.py",
        "utils/visualization.py",
        "components/styling.py",
        "pages/01_🏠_Dashboard.py",
        "pages/02_📊_Data_Explorer.py",
        "pages/03_🤖_Model_Comparison.py",
        "pages/04_🔮_Prediction.py",
        "pages/05_📈_Analytics.py",
        "pages/06_📋_Reports.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    return len(missing_files) == 0, missing_files

def test_data_files():
    """Test if data files exist in parent directory"""
    print("\n📊 Testing data files...")
    
    parent_dir = Path(__file__).parent.parent
    
    data_files = [
        "reviews_gofood_Merchant.xlsx",
        "model_random_forest.pkl",
        "model_logistic_regression.pkl",
        "tfidf_vectorizer_tuned.pkl"
    ]
    
    missing_data = []
    
    for file_name in data_files:
        file_path = parent_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            missing_data.append(file_name)
            print(f"❌ {file_name}")
    
    return len(missing_data) == 0, missing_data

def test_config_loading():
    """Test if configuration files can be loaded"""
    print("\n⚙️ Testing configuration loading...")
    
    try:
        from streamlit.config import settings, constants
        print("✅ Settings module loaded")
        print("✅ Constants module loaded")
        
        # Test specific configurations
        if hasattr(settings, 'APP_CONFIG'):
            print("✅ APP_CONFIG found")
        else:
            print("❌ APP_CONFIG missing")
            
        if hasattr(constants, 'COLUMNS'):
            print("✅ COLUMNS found")
        else:
            print("❌ COLUMNS missing")
            
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions"""
    print("\n🔧 Testing utility functions...")
    
    try:
        # Test data loader
        from streamlit.utils.data_loader import load_raw_data, get_data_summary
        print("✅ Data loader functions imported")
        
        # Test model utils
        from streamlit.utils.model_utils import predict_sentiment, load_all_models
        print("✅ Model utility functions imported")
        
        # Test preprocessing
        from streamlit.utils.preprocessing import clean_text, preprocess_text
        print("✅ Preprocessing functions imported")
        
        # Test visualization
        from streamlit.utils.visualization import create_sentiment_distribution_chart
        print("✅ Visualization functions imported")
        
        # Test basic functionality
        sample_text = "Aplikasi sangat bagus dan mudah digunakan!"
        cleaned = clean_text(sample_text)
        processed = preprocess_text(sample_text)
        
        if cleaned and processed:
            print("✅ Text processing functions work")
        else:
            print("❌ Text processing functions failed")
            
        return True
        
    except Exception as e:
        print(f"❌ Utility function test failed: {e}")
        return False

def test_sample_data_processing():
    """Test data processing with sample data"""
    print("\n📊 Testing sample data processing...")
    
    try:
        # Create sample data
        sample_data = pd.DataFrame({
            'rating': [5, 1, 3, 4, 2],
            'ulasan': [
                'Aplikasi sangat bagus dan mudah digunakan!',
                'Pelayanan buruk, makanan terlambat datang',
                'Biasa saja, tidak ada yang istimewa',
                'Cukup baik, tapi masih bisa diperbaiki',
                'Tidak puas dengan layanan ini'
            ]
        })
        
        print("✅ Sample data created")
        
        # Test data summary
        from streamlit.utils.data_loader import get_data_summary, add_sentiment_labels
        
        summary = get_data_summary(sample_data)
        if summary:
            print("✅ Data summary generation works")
        else:
            print("❌ Data summary generation failed")
        
        # Test sentiment labeling
        labeled_data = add_sentiment_labels(sample_data)
        if 'sentiment_label' in labeled_data.columns:
            print("✅ Sentiment labeling works")
        else:
            print("❌ Sentiment labeling failed")
        
        # Test preprocessing
        from streamlit.utils.preprocessing import preprocess_dataframe
        
        processed_data = preprocess_dataframe(sample_data, 'ulasan')
        if not processed_data.empty:
            print("✅ Data preprocessing works")
        else:
            print("❌ Data preprocessing failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample data processing failed: {e}")
        return False

def test_visualization_creation():
    """Test visualization creation"""
    print("\n📈 Testing visualization creation...")
    
    try:
        # Create sample data
        sample_data = pd.DataFrame({
            'rating': [5, 1, 3, 4, 2, 5, 4, 3, 2, 1],
            'sentiment_label': ['Positif', 'Negatif', 'Netral', 'Positif', 'Negatif',
                              'Positif', 'Positif', 'Netral', 'Negatif', 'Negatif']
        })
        
        from streamlit.utils.visualization import (
            create_sentiment_distribution_chart,
            create_rating_distribution_chart,
            create_wordcloud
        )
        
        # Test sentiment chart
        fig_sentiment = create_sentiment_distribution_chart(sample_data, 'sentiment_label')
        if fig_sentiment:
            print("✅ Sentiment distribution chart created")
        else:
            print("❌ Sentiment distribution chart failed")
        
        # Test rating chart
        fig_rating = create_rating_distribution_chart(sample_data, 'rating')
        if fig_rating:
            print("✅ Rating distribution chart created")
        else:
            print("❌ Rating distribution chart failed")
        
        # Test word cloud
        sample_texts = ['aplikasi bagus', 'layanan buruk', 'biasa saja']
        wordcloud = create_wordcloud(sample_texts)
        if wordcloud:
            print("✅ Word cloud created")
        else:
            print("❌ Word cloud creation failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization test failed: {e}")
        return False

def test_streamlit_app():
    """Test if Streamlit app can be imported"""
    print("\n🚀 Testing Streamlit app...")
    
    try:
        app_file = Path(__file__).parent / "app.py"
        
        if not app_file.exists():
            print("❌ app.py not found")
            return False
        
        # Try to load the app module
        spec = importlib.util.spec_from_file_location("app", app_file)
        if spec is None:
            print("❌ Could not load app.py")
            return False
        
        print("✅ app.py can be loaded")
        
        # Check for main function
        app_module = importlib.util.module_from_spec(spec)
        if hasattr(app_module, 'main'):
            print("✅ main() function found")
        else:
            print("⚠️ main() function not found (may be normal)")
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit app test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("🧪 GoFood Sentiment Analysis Dashboard - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Package Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Data Files", test_data_files),
        ("Configuration Loading", test_config_loading),
        ("Utility Functions", test_utility_functions),
        ("Sample Data Processing", test_sample_data_processing),
        ("Visualization Creation", test_visualization_creation),
        ("Streamlit App", test_streamlit_app)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, details = result
                results.append((test_name, success, details))
            else:
                results.append((test_name, result, None))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False, str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, success, details in results:
        if success:
            print(f"✅ {test_name}")
            passed += 1
        else:
            print(f"❌ {test_name}")
            if details:
                if isinstance(details, list):
                    for detail in details[:3]:  # Show first 3 details
                        print(f"   • {detail}")
                    if len(details) > 3:
                        print(f"   • ... and {len(details) - 3} more")
                else:
                    print(f"   • {details}")
            failed += 1
    
    print(f"\n📊 Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Dashboard is ready to use.")
        print("🚀 Run: python launch.py")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        print("💡 You may still be able to run the dashboard with limited functionality.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
