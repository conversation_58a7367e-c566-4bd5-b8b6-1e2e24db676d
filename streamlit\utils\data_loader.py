"""
Data loading utilities for the GoFood Sentiment Analysis Dashboard
"""

import pandas as pd
import numpy as np
import streamlit as st
from pathlib import Path
import joblib
from typing import Optional, Dict, Any, List
import logging
import re
import string

from config.settings import DATA_FILES, MODEL_FILES
from config.constants import COLUMNS, RATING_SENTIMENT_MAP

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@st.cache_data(ttl=3600)
def load_raw_data() -> Optional[pd.DataFrame]:
    """
    Load raw reviews data from Excel file
    
    Returns:
        pd.DataFrame: Raw reviews data or None if file not found
    """
    try:
        file_path = DATA_FILES['raw_reviews']
        if not file_path.exists():
            st.error(f"Data file not found: {file_path}")
            return None
            
        df = pd.read_excel(file_path)
        logger.info(f"Loaded raw data: {len(df)} records")
        return df
        
    except Exception as e:
        st.error(f"Error loading raw data: {str(e)}")
        logger.error(f"Error loading raw data: {str(e)}")
        return None

@st.cache_data(ttl=3600)
def load_preprocessed_data() -> Optional[pd.DataFrame]:
    """
    Load preprocessed reviews data from Excel file
    
    Returns:
        pd.DataFrame: Preprocessed reviews data or None if file not found
    """
    try:
        file_path = DATA_FILES['preprocessed_reviews']
        if not file_path.exists():
            st.warning(f"Preprocessed data file not found: {file_path}")
            return None
            
        df = pd.read_excel(file_path)
        logger.info(f"Loaded preprocessed data: {len(df)} records")
        return df
        
    except Exception as e:
        st.error(f"Error loading preprocessed data: {str(e)}")
        logger.error(f"Error loading preprocessed data: {str(e)}")
        return None

@st.cache_resource
def load_model(model_name: str) -> Optional[Any]:
    """
    Load a trained model from pickle file
    
    Args:
        model_name: Name of the model to load
        
    Returns:
        Loaded model or None if file not found
    """
    try:
        if model_name not in MODEL_FILES:
            st.error(f"Unknown model: {model_name}")
            return None
            
        file_path = MODEL_FILES[model_name]
        if not file_path.exists():
            st.warning(f"Model file not found: {file_path}")
            return None
            
        model = joblib.load(file_path)
        logger.info(f"Loaded model: {model_name}")
        return model
        
    except Exception as e:
        st.error(f"Error loading model {model_name}: {str(e)}")
        logger.error(f"Error loading model {model_name}: {str(e)}")
        return None

@st.cache_data(ttl=3600)
def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Get summary statistics for the dataset
    
    Args:
        df: DataFrame to analyze
        
    Returns:
        Dictionary with summary statistics
    """
    try:
        summary = {
            'total_records': len(df),
            'columns': list(df.columns),
            'missing_values': df.isnull().sum().to_dict(),
            'data_types': df.dtypes.to_dict(),
        }
        
        # Add rating distribution if rating column exists
        if COLUMNS['rating'] in df.columns:
            summary['rating_distribution'] = df[COLUMNS['rating']].value_counts().to_dict()
            
        # Add sentiment distribution if sentiment column exists
        if COLUMNS['sentiment'] in df.columns:
            summary['sentiment_distribution'] = df[COLUMNS['sentiment']].value_counts().to_dict()
            
        return summary
        
    except Exception as e:
        logger.error(f"Error getting data summary: {str(e)}")
        return {}

def add_sentiment_labels(df: pd.DataFrame) -> pd.DataFrame:
    """
    Add sentiment labels based on ratings
    
    Args:
        df: DataFrame with rating column
        
    Returns:
        DataFrame with sentiment labels added
    """
    try:
        df_copy = df.copy()
        
        if COLUMNS['rating'] in df_copy.columns:
            df_copy['sentiment_label'] = df_copy[COLUMNS['rating']].map(RATING_SENTIMENT_MAP)
            
        return df_copy
        
    except Exception as e:
        logger.error(f"Error adding sentiment labels: {str(e)}")
        return df

@st.cache_data(ttl=3600)
def filter_data(df: pd.DataFrame, filters: Dict[str, Any]) -> pd.DataFrame:
    """
    Filter dataframe based on provided filters
    
    Args:
        df: DataFrame to filter
        filters: Dictionary of filter criteria
        
    Returns:
        Filtered DataFrame
    """
    try:
        filtered_df = df.copy()
        
        # Filter by rating
        if 'rating' in filters and filters['rating']:
            filtered_df = filtered_df[filtered_df[COLUMNS['rating']].isin(filters['rating'])]
            
        # Filter by sentiment
        if 'sentiment' in filters and filters['sentiment']:
            filtered_df = filtered_df[filtered_df['sentiment_label'].isin(filters['sentiment'])]
            
        # Filter by text length
        if 'min_length' in filters and filters['min_length']:
            text_col = COLUMNS['review']
            if text_col in filtered_df.columns:
                filtered_df = filtered_df[filtered_df[text_col].str.len() >= filters['min_length']]
                
        if 'max_length' in filters and filters['max_length']:
            text_col = COLUMNS['review']
            if text_col in filtered_df.columns:
                filtered_df = filtered_df[filtered_df[text_col].str.len() <= filters['max_length']]
                
        return filtered_df

    except Exception as e:
        logger.error(f"Error filtering data: {str(e)}")
        return df

def preprocess_text_basic(text: str) -> str:
    """
    Basic text preprocessing for compatibility
    """
    try:
        if pd.isna(text) or text is None:
            return ""

        text = str(text).lower()

        # Remove URLs, emails, mentions
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        text = re.sub(r'\S+@\S+', '', text)
        text = re.sub(r'@\w+|#\w+', '', text)

        # Remove numbers and punctuation
        text = re.sub(r'\d+', '', text)
        text = text.translate(str.maketrans('', '', string.punctuation))

        # Remove extra whitespace
        text = ' '.join(text.split())

        return text.strip()

    except Exception as e:
        logger.error(f"Error preprocessing text: {str(e)}")
        return str(text) if text else ""

def create_processed_dataset(df: pd.DataFrame, text_column: str = 'ulasan') -> pd.DataFrame:
    """
    Create a processed version of the dataset with basic preprocessing
    """
    try:
        df_processed = df.copy()

        if text_column not in df.columns:
            logger.error(f"Column '{text_column}' not found")
            return df_processed

        # Apply basic preprocessing
        df_processed['processed_text'] = df_processed[text_column].apply(preprocess_text_basic)

        # Remove empty processed texts
        df_processed = df_processed[df_processed['processed_text'].str.len() > 0]

        logger.info(f"Created processed dataset with {len(df_processed)} records")

        return df_processed

    except Exception as e:
        logger.error(f"Error creating processed dataset: {str(e)}")
        return df

def validate_data(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Validate data quality and return validation results
    
    Args:
        df: DataFrame to validate
        
    Returns:
        Dictionary with validation results
    """
    try:
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        # Check if dataframe is empty
        if df.empty:
            validation['is_valid'] = False
            validation['errors'].append("Dataset is empty")
            return validation
            
        # Check for required columns
        required_cols = [COLUMNS['review'], COLUMNS['rating']]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            validation['is_valid'] = False
            validation['errors'].append(f"Missing required columns: {missing_cols}")
            
        # Check for missing values in critical columns
        for col in required_cols:
            if col in df.columns:
                missing_count = df[col].isnull().sum()
                if missing_count > 0:
                    validation['warnings'].append(f"Column '{col}' has {missing_count} missing values")
                    
        # Check data types
        if COLUMNS['rating'] in df.columns:
            if not pd.api.types.is_numeric_dtype(df[COLUMNS['rating']]):
                validation['warnings'].append("Rating column is not numeric")
                
        validation['info'].append(f"Dataset contains {len(df)} records")
        validation['info'].append(f"Dataset has {len(df.columns)} columns")
        
        return validation
        
    except Exception as e:
        logger.error(f"Error validating data: {str(e)}")
        return {
            'is_valid': False,
            'errors': [f"Validation error: {str(e)}"],
            'warnings': [],
            'info': []
        }
