"""
Model utilities for the GoFood Sentiment Analysis Dashboard
"""

import pandas as pd
import numpy as np
import streamlit as st
from typing import Dict, List, Tuple, Any, Optional
import joblib
import logging
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
from sklearn.feature_extraction.text import TfidfVectorizer

from config.settings import MODEL_FILES
from config.constants import MODEL_NAMES, SENTIMENT_LABELS, EVALUATION_METRICS
from utils.data_loader import load_model

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@st.cache_resource
def load_all_models() -> Dict[str, Any]:
    """
    Load all available models
    
    Returns:
        Dictionary of loaded models
    """
    models = {}
    
    for model_key, model_name in MODEL_NAMES.items():
        model = load_model(model_key)
        if model is not None:
            models[model_key] = model
            
    logger.info(f"Loaded {len(models)} models")
    return models

@st.cache_resource
def load_vectorizer() -> Optional[TfidfVectorizer]:
    """
    Load the TF-IDF vectorizer
    
    Returns:
        Loaded vectorizer or None if not found
    """
    return load_model('tfidf_vectorizer')

def preprocess_text_for_prediction(text: str) -> str:
    """
    Preprocess text for model prediction
    
    Args:
        text: Raw text to preprocess
        
    Returns:
        Preprocessed text
    """
    try:
        # Basic preprocessing - this should match the training preprocessing
        text = str(text).lower().strip()
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
        
    except Exception as e:
        logger.error(f"Error preprocessing text: {str(e)}")
        return text

def predict_sentiment(text: str, model_name: str = 'best_model') -> Dict[str, Any]:
    """
    Predict sentiment for a single text
    
    Args:
        text: Text to analyze
        model_name: Name of the model to use
        
    Returns:
        Dictionary with prediction results
    """
    try:
        # Load model and vectorizer
        model = load_model(model_name)
        vectorizer = load_vectorizer()
        
        if model is None or vectorizer is None:
            return {
                'error': 'Model or vectorizer not available',
                'prediction': None,
                'confidence': None,
                'probabilities': None
            }
            
        # Preprocess text
        processed_text = preprocess_text_for_prediction(text)
        
        # Vectorize text
        text_vector = vectorizer.transform([processed_text])
        
        # Make prediction
        prediction = model.predict(text_vector)[0]
        
        # Get prediction probabilities if available
        probabilities = None
        confidence = None
        
        if hasattr(model, 'predict_proba'):
            proba = model.predict_proba(text_vector)[0]
            probabilities = dict(zip(model.classes_, proba))
            confidence = max(proba)
            
        return {
            'prediction': prediction,
            'confidence': confidence,
            'probabilities': probabilities,
            'processed_text': processed_text,
            'error': None
        }
        
    except Exception as e:
        logger.error(f"Error predicting sentiment: {str(e)}")
        return {
            'error': str(e),
            'prediction': None,
            'confidence': None,
            'probabilities': None
        }

def predict_batch(texts: List[str], model_name: str = 'best_model') -> List[Dict[str, Any]]:
    """
    Predict sentiment for multiple texts
    
    Args:
        texts: List of texts to analyze
        model_name: Name of the model to use
        
    Returns:
        List of prediction results
    """
    try:
        # Load model and vectorizer
        model = load_model(model_name)
        vectorizer = load_vectorizer()
        
        if model is None or vectorizer is None:
            return [{'error': 'Model or vectorizer not available'} for _ in texts]
            
        # Preprocess texts
        processed_texts = [preprocess_text_for_prediction(text) for text in texts]
        
        # Vectorize texts
        text_vectors = vectorizer.transform(processed_texts)
        
        # Make predictions
        predictions = model.predict(text_vectors)
        
        # Get probabilities if available
        probabilities = None
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(text_vectors)
            
        results = []
        for i, (text, prediction) in enumerate(zip(texts, predictions)):
            result = {
                'original_text': text,
                'processed_text': processed_texts[i],
                'prediction': prediction,
                'error': None
            }
            
            if probabilities is not None:
                proba = probabilities[i]
                result['probabilities'] = dict(zip(model.classes_, proba))
                result['confidence'] = max(proba)
                
            results.append(result)
            
        return results
        
    except Exception as e:
        logger.error(f"Error in batch prediction: {str(e)}")
        return [{'error': str(e)} for _ in texts]

def evaluate_model(model, X_test, y_test) -> Dict[str, float]:
    """
    Evaluate model performance
    
    Args:
        model: Trained model
        X_test: Test features
        y_test: Test labels
        
    Returns:
        Dictionary with evaluation metrics
    """
    try:
        # Make predictions
        y_pred = model.predict(X_test)
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Error evaluating model: {str(e)}")
        return {}

def get_confusion_matrix(model, X_test, y_test) -> Tuple[np.ndarray, List[str]]:
    """
    Get confusion matrix for model predictions
    
    Args:
        model: Trained model
        X_test: Test features
        y_test: Test labels
        
    Returns:
        Tuple of (confusion matrix, class labels)
    """
    try:
        y_pred = model.predict(X_test)
        cm = confusion_matrix(y_test, y_pred)
        labels = sorted(list(set(y_test) | set(y_pred)))
        
        return cm, labels
        
    except Exception as e:
        logger.error(f"Error getting confusion matrix: {str(e)}")
        return np.array([]), []

def get_classification_report(model, X_test, y_test) -> str:
    """
    Get detailed classification report
    
    Args:
        model: Trained model
        X_test: Test features
        y_test: Test labels
        
    Returns:
        Classification report as string
    """
    try:
        y_pred = model.predict(X_test)
        report = classification_report(y_test, y_pred)
        
        return report
        
    except Exception as e:
        logger.error(f"Error getting classification report: {str(e)}")
        return ""

def get_feature_importance(model, feature_names: List[str], top_n: int = 20) -> Dict[str, float]:
    """
    Get feature importance from model
    
    Args:
        model: Trained model
        feature_names: List of feature names
        top_n: Number of top features to return
        
    Returns:
        Dictionary of feature importance scores
    """
    try:
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
        elif hasattr(model, 'coef_'):
            # For linear models, use absolute values of coefficients
            importances = np.abs(model.coef_[0])
        else:
            return {}
            
        # Get top features
        indices = np.argsort(importances)[::-1][:top_n]
        top_features = {
            feature_names[i]: importances[i] 
            for i in indices 
            if i < len(feature_names)
        }
        
        return top_features
        
    except Exception as e:
        logger.error(f"Error getting feature importance: {str(e)}")
        return {}

@st.cache_data(ttl=3600)
def compare_models(models: Dict[str, Any], X_test, y_test) -> pd.DataFrame:
    """
    Compare performance of multiple models
    
    Args:
        models: Dictionary of models to compare
        X_test: Test features
        y_test: Test labels
        
    Returns:
        DataFrame with model comparison results
    """
    try:
        results = []
        
        for model_name, model in models.items():
            if model is None:
                continue
                
            metrics = evaluate_model(model, X_test, y_test)
            metrics['model'] = MODEL_NAMES.get(model_name, model_name)
            results.append(metrics)
            
        df_results = pd.DataFrame(results)
        
        if not df_results.empty:
            # Sort by accuracy (or another metric)
            df_results = df_results.sort_values('accuracy', ascending=False)
            
        return df_results
        
    except Exception as e:
        logger.error(f"Error comparing models: {str(e)}")
        return pd.DataFrame()

def get_model_info(model_name: str) -> Dict[str, Any]:
    """
    Get information about a specific model
    
    Args:
        model_name: Name of the model
        
    Returns:
        Dictionary with model information
    """
    try:
        model = load_model(model_name)
        
        if model is None:
            return {'error': 'Model not found'}
            
        info = {
            'name': MODEL_NAMES.get(model_name, model_name),
            'type': type(model).__name__,
            'parameters': getattr(model, 'get_params', lambda: {})(),
        }
        
        # Add model-specific information
        if hasattr(model, 'n_estimators'):
            info['n_estimators'] = model.n_estimators
            
        if hasattr(model, 'max_depth'):
            info['max_depth'] = model.max_depth
            
        if hasattr(model, 'C'):
            info['C'] = model.C
            
        return info
        
    except Exception as e:
        logger.error(f"Error getting model info: {str(e)}")
        return {'error': str(e)}
