"""
Text preprocessing utilities for the GoFood Sentiment Analysis Dashboard
"""

import re
import string
import pandas as pd
import streamlit as st
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Indonesian stopwords (basic set)
INDONESIAN_STOPWORDS = {
    'ada', 'adalah', 'adanya', 'adapun', 'agak', 'agaknya', 'agar', 'akan', 'akankah', 'akhir',
    'akhiri', 'akhirnya', 'aku', 'akulah', 'amat', 'amatlah', 'anda', 'andalah', 'antar',
    'antara', 'antaranya', 'apa', 'apaan', 'apabila', 'apakah', 'apalagi', 'apatah', 'artinya',
    'asal', 'asalkan', 'atas', 'atau', 'ataukah', 'ataupun', 'awal', 'awalnya', 'bagai',
    'bagaikan', 'bagaimana', 'bagaimanakah', 'bagaimanapun', 'bagi', 'bagian', 'bahkan',
    'bahwa', 'bahwasanya', 'baik', 'bakal', 'bakalan', 'balik', 'banyak', 'bapak', 'baru',
    'bawah', 'beberapa', 'begini', 'beginian', 'beginikah', 'beginilah', 'begitu', 'begitukah',
    'begitulah', 'begitupun', 'bekerja', 'belakang', 'belakangan', 'belum', 'belumlah',
    'benar', 'benarkah', 'benarlah', 'berada', 'berakhir', 'berakhirlah', 'berakhirnya',
    'berapa', 'berapakah', 'berapalah', 'berapapun', 'berarti', 'berawal', 'berbagai',
    'berdatangan', 'beri', 'berikan', 'berikut', 'berikutnya', 'berjumlah', 'berkali',
    'berkata', 'berkehendak', 'berkeinginan', 'berkenaan', 'berlainan', 'berlalu', 'berlangsung',
    'berlebihan', 'bermacam', 'bermaksud', 'bermula', 'bersama', 'bersedia', 'bersiap',
    'bersiap-siap', 'bertanya', 'bertanya-tanya', 'berturut', 'berturut-turut', 'bertutur',
    'berujar', 'berupa', 'besar', 'betul', 'betulkah', 'biasa', 'biasanya', 'bila', 'bilakah',
    'bisa', 'bisakah', 'boleh', 'bolehkah', 'bukan', 'bukankah', 'bukanlah', 'bukannya',
    'bulan', 'bung', 'cara', 'caranya', 'cukup', 'cukupkah', 'cukuplah', 'cuma', 'dahulu',
    'dalam', 'dan', 'dapat', 'dari', 'daripada', 'datang', 'dekat', 'demi', 'demikian',
    'demikianlah', 'dengan', 'depan', 'di', 'dia', 'diakhiri', 'diakhirinya', 'dialah',
    'diantara', 'diantaranya', 'diberi', 'diberikan', 'diberikannya', 'dibuat', 'dibuatnya',
    'didapat', 'didatangkan', 'digunakan', 'diibaratkan', 'diibaratkannya', 'diingat',
    'diingatkan', 'diinginkan', 'dijawab', 'dijelaskan', 'dijelaskannya', 'dikarenakan',
    'dikatakan', 'dikatakannya', 'dikerjakan', 'diketahui', 'diketahuinya', 'dikira',
    'dilakukan', 'dilalui', 'dilihat', 'dimaksud', 'dimaksudkan', 'dimaksudkannya',
    'dimaksudnya', 'diminta', 'dimintai', 'dimisalkan', 'dimulai', 'dimulailah', 'dimulainya',
    'dimungkinkan', 'dini', 'dipastikan', 'diperbuat', 'diperbuatnya', 'dipergunakan',
    'diperkirakan', 'diperlihatkan', 'diperlukan', 'diperlukannya', 'dipersoalkan',
    'dipertanyakan', 'dipunyai', 'diri', 'dirinya', 'disampaikan', 'disebut', 'disebutkan',
    'disebutkannya', 'disini', 'disinilah', 'ditambahkan', 'ditandaskan', 'ditanya',
    'ditanyai', 'ditanyakan', 'ditegaskan', 'ditujukan', 'ditunjuk', 'ditunjuki',
    'ditunjukkan', 'ditunjukkannya', 'ditunjuknya', 'dituturkan', 'dituturkannya', 'diucapkan',
    'diucapkannya', 'diungkapkan', 'dong', 'dua', 'dulu', 'empat', 'enggak', 'enggaknya',
    'entah', 'entahlah', 'guna', 'gunakan', 'hal', 'hampir', 'hanya', 'hanyalah', 'hari',
    'harus', 'haruslah', 'harusnya', 'hendak', 'hendaklah', 'hendaknya', 'hingga', 'ia',
    'ialah', 'ibarat', 'ibaratkan', 'ibaratnya', 'ibu', 'ikut', 'ingat', 'ingat-ingat',
    'ingin', 'inginkah', 'inginkan', 'ini', 'inikah', 'inilah', 'itu', 'itukah', 'itulah',
    'jadi', 'jadilah', 'jadinya', 'jangan', 'jangankan', 'janganlah', 'jauh', 'jawab',
    'jawaban', 'jawabnya', 'jelas', 'jelaskan', 'jelaslah', 'jelasnya', 'jika', 'jikalau',
    'juga', 'jumlah', 'jumlahnya', 'justru', 'kala', 'kalau', 'kalaulah', 'kalaupun',
    'kalian', 'kami', 'kamilah', 'kamu', 'kamulah', 'kan', 'kapan', 'kapankah', 'kapanpun',
    'karena', 'karenanya', 'kasus', 'kata', 'katakan', 'katakanlah', 'katanya', 'ke', 'keadaan',
    'kebetulan', 'kecil', 'kedua', 'keduanya', 'keinginan', 'kelamaan', 'kelihatan',
    'kelihatannya', 'kelima', 'keluar', 'kembali', 'kemudian', 'kemungkinan', 'kemungkinannya',
    'kenapa', 'kepada', 'kepadanya', 'kesampaian', 'keseluruhan', 'keseluruhannya', 'ketika',
    'ketujuh', 'kira', 'kira-kira', 'kiranya', 'kita', 'kitalah', 'kok', 'kurang', 'lagi',
    'lagian', 'lah', 'lain', 'lainnya', 'lalu', 'lama', 'lamanya', 'lanjut', 'lanjutnya',
    'lebih', 'lewat', 'lima', 'luar', 'macam', 'maka', 'makanya', 'makin', 'malah', 'malahan',
    'mampu', 'mampukah', 'mana', 'manakala', 'manalagi', 'masa', 'masalah', 'masalahnya',
    'masih', 'masihkah', 'masing', 'masing-masing', 'mau', 'maupun', 'melainkan', 'melakukan',
    'melalui', 'melihat', 'melihatnya', 'memang', 'memastikan', 'memberi', 'memberikan',
    'membuat', 'memerlukan', 'memihak', 'meminta', 'memintakan', 'memisalkan', 'memperbuat',
    'mempergunakan', 'memperkirakan', 'memperlihatkan', 'mempersiapkan', 'mempersoalkan',
    'mempertanyakan', 'mempunyai', 'memulai', 'memungkinkan', 'menaiki', 'menambahkan',
    'menandaskan', 'menanti', 'menanya', 'menanyai', 'menanyakan', 'mendapat', 'mendapatkan',
    'mendatang', 'mendatangi', 'mendatangkan', 'menegaskan', 'mengakhiri', 'mengapa',
    'mengatakan', 'mengatakannya', 'mengenai', 'mengerjakan', 'mengetahui', 'menggunakan',
    'menghendaki', 'mengibaratkan', 'mengibaratkannya', 'mengingat', 'mengingatkan',
    'menginginkan', 'mengira', 'mengucapkan', 'mengucapkannya', 'mengungkapkan', 'menjadi',
    'menjawab', 'menjelaskan', 'menuju', 'menunjuk', 'menunjuki', 'menunjukkan', 'menunjuknya',
    'menurut', 'menuturkan', 'menyampaikan', 'menyangkut', 'menyatakan', 'menyebutkan',
    'menyeluruh', 'menyiapkan', 'merasa', 'mereka', 'merekalah', 'merupakan', 'meski',
    'meskipun', 'meyakini', 'meyakinkan', 'minta', 'mirip', 'misal', 'misalkan', 'misalnya',
    'mula', 'mulai', 'mulailah', 'mulanya', 'mungkin', 'mungkinkah', 'nah', 'naik', 'namun',
    'nanti', 'nantinya', 'nyaris', 'oleh', 'olehnya', 'pada', 'padahal', 'padanya', 'pak',
    'paling', 'panjang', 'pantas', 'para', 'pasti', 'pastilah', 'penting', 'pentingnya',
    'per', 'percuma', 'perlu', 'perlukah', 'perlunya', 'pernah', 'persoalan', 'pertama',
    'pertama-tama', 'pertanyaan', 'pertanyakan', 'pihak', 'pihaknya', 'pukul', 'pula',
    'pun', 'punya', 'punyakah', 'rah', 'rasa', 'rasanya', 'rata', 'rupanya', 'saat',
    'saatnya', 'saja', 'sajalah', 'saling', 'sama', 'sama-sama', 'sambil', 'sampai',
    'sampai-sampai', 'sampaikan', 'sana', 'sangat', 'sangatlah', 'satu', 'saya', 'sayalah',
    'se', 'sebab', 'sebabnya', 'sebagai', 'sebagaimana', 'sebagainya', 'sebagian', 'sebaik',
    'sebaik-baiknya', 'sebaiknya', 'sebaliknya', 'sebanyak', 'sebegini', 'sebegitu',
    'sebelum', 'sebelumnya', 'sebenarnya', 'seberapa', 'sebesar', 'sebetulnya', 'sebisanya',
    'sebuah', 'sebut', 'sebutlah', 'sebutnya', 'secara', 'secukupnya', 'sedang', 'sedangkan',
    'sedemikian', 'sedikit', 'sedikitnya', 'seenaknya', 'segala', 'segalanya', 'segera',
    'seharusnya', 'sehingga', 'seingat', 'sejak', 'sejauh', 'sejenak', 'sejumlah', 'sekadar',
    'sekadarnya', 'sekali', 'sekali-kali', 'sekalian', 'sekaligus', 'sekalipun', 'sekarang',
    'sekarang', 'sekecil', 'seketika', 'sekiranya', 'sekitar', 'sekitarnya', 'sekurang',
    'sekurang-kurangnya', 'sekurangnya', 'sela', 'selain', 'selaku', 'selalu', 'selama',
    'selama-lamanya', 'selamanya', 'selanjutnya', 'seluruh', 'seluruhnya', 'semacam',
    'semakin', 'semampu', 'semampunya', 'semasa', 'semasih', 'semata', 'semata-mata',
    'semaunya', 'sementara', 'semisal', 'semisalnya', 'sempat', 'semua', 'semuanya',
    'semula', 'sendiri', 'sendirian', 'sendirinya', 'seolah', 'seolah-olah', 'seorang',
    'sepanjang', 'sepantasnya', 'sepantasnyalah', 'seperlunya', 'seperti', 'sepertinya',
    'sepihak', 'sering', 'seringnya', 'serta', 'serupa', 'sesaat', 'sesama', 'sesampai',
    'sesegera', 'sesekali', 'seseorang', 'sesuai', 'sesuatu', 'sesuatunya', 'sesudah',
    'sesudahnya', 'setelah', 'setempat', 'setengah', 'seterusnya', 'setiap', 'setiba',
    'setibanya', 'setidak-tidaknya', 'setidaknya', 'setinggi', 'seusai', 'sewaktu', 'siap',
    'siapa', 'siapakah', 'siapapun', 'sih', 'sini', 'sinilah', 'soal', 'soalnya', 'suatu',
    'sudah', 'sudahkah', 'sudahlah', 'supaya', 'tadi', 'tadinya', 'tahu', 'tahun', 'tak',
    'tambah', 'tambahnya', 'tampak', 'tampaknya', 'tandas', 'tandasnya', 'tanpa', 'tanya',
    'tanyakan', 'tanyanya', 'tapi', 'tegas', 'tegasnya', 'telah', 'tempat', 'tengah',
    'tentang', 'tentu', 'tentulah', 'tentunya', 'tepat', 'terakhir', 'terasa', 'terbanyak',
    'terdahulu', 'terdapat', 'terdiri', 'terhadap', 'terhadapnya', 'teringat', 'teringat-ingat',
    'terjadi', 'terjadilah', 'terjadinya', 'terkira', 'terlalu', 'terlebih', 'terlihat',
    'termasuk', 'ternyata', 'tersampaikan', 'tersebut', 'tersebutlah', 'tertentu', 'tertuju',
    'terus', 'terutama', 'tetap', 'tetapi', 'tiap', 'tiba', 'tiba-tiba', 'tidak', 'tidakkah',
    'tidaklah', 'tiga', 'tinggi', 'toh', 'tunjuk', 'turut', 'tutur', 'tuturnya', 'ucap',
    'ucapnya', 'ujar', 'ujarnya', 'umum', 'umumnya', 'ungkap', 'ungkapnya', 'untuk',
    'usah', 'usai', 'waduh', 'wah', 'wahai', 'waktu', 'waktunya', 'walau', 'walaupun',
    'wong', 'yaitu', 'yakin', 'yakni', 'yang'
}

# Common Indonesian slang and abbreviations
SLANG_DICT = {
    'gak': 'tidak',
    'ga': 'tidak', 
    'ngga': 'tidak',
    'nggak': 'tidak',
    'gk': 'tidak',
    'tdk': 'tidak',
    'gw': 'saya',
    'gue': 'saya',
    'ane': 'saya',
    'w': 'saya',
    'lu': 'kamu',
    'lo': 'kamu',
    'elu': 'kamu',
    'u': 'kamu',
    'yg': 'yang',
    'dgn': 'dengan',
    'utk': 'untuk',
    'krn': 'karena',
    'krna': 'karena',
    'karna': 'karena',
    'tp': 'tapi',
    'tpi': 'tapi',
    'bgt': 'banget',
    'bgt': 'banget',
    'bener': 'benar',
    'bnr': 'benar',
    'udh': 'sudah',
    'udah': 'sudah',
    'dah': 'sudah',
    'blm': 'belum',
    'blom': 'belum',
    'jg': 'juga',
    'jga': 'juga',
    'emg': 'memang',
    'emang': 'memang',
    'gmn': 'bagaimana',
    'gimana': 'bagaimana',
    'knp': 'kenapa',
    'knapa': 'kenapa',
    'kpn': 'kapan',
    'kapan': 'kapan',
    'dmn': 'dimana',
    'dimana': 'dimana',
    'org': 'orang',
    'orng': 'orang',
    'hrs': 'harus',
    'mst': 'harus',
    'bs': 'bisa',
    'bsa': 'bisa',
    'klo': 'kalau',
    'kalo': 'kalau',
    'kl': 'kalau',
    'dr': 'dari',
    'dri': 'dari',
    'ke': 'ke',
    'sm': 'sama',
    'sma': 'sama',
    'lg': 'lagi',
    'lgi': 'lagi',
    'aj': 'saja',
    'aja': 'saja',
    'doang': 'saja',
    'kok': 'kok',
    'sih': 'sih',
    'deh': 'deh',
    'dong': 'dong',
    'lah': 'lah',
    'tuh': 'itu',
    'tu': 'itu',
    'ni': 'ini',
    'nih': 'ini',
    'gt': 'begitu',
    'gtu': 'begitu',
    'gitu': 'begitu',
    'bgtu': 'begitu',
    'bgitu': 'begitu',
    'mksd': 'maksud',
    'mksud': 'maksud',
    'mkny': 'makanya',
    'makany': 'makanya',
    'krg': 'kurang',
    'kurang': 'kurang',
    'lbh': 'lebih',
    'lebih': 'lebih',
    'bnyk': 'banyak',
    'banyak': 'banyak',
    'sdikit': 'sedikit',
    'sedikit': 'sedikit',
    'skrg': 'sekarang',
    'skrang': 'sekarang',
    'skg': 'sekarang',
    'td': 'tadi',
    'tdi': 'tadi',
    'kmrn': 'kemarin',
    'kemarin': 'kemarin',
    'bsk': 'besok',
    'besok': 'besok',
    'pgi': 'pagi',
    'pagi': 'pagi',
    'mlm': 'malam',
    'malem': 'malam',
    'siang': 'siang',
    'sore': 'sore',
    'thx': 'terima kasih',
    'thanks': 'terima kasih',
    'makasih': 'terima kasih',
    'mksh': 'terima kasih',
    'tq': 'terima kasih',
    'ty': 'terima kasih',
    'ok': 'oke',
    'oke': 'oke',
    'okey': 'oke',
    'okay': 'oke',
    'mantap': 'bagus',
    'mantul': 'bagus',
    'keren': 'bagus',
    'bagus': 'bagus',
    'jelek': 'buruk',
    'buruk': 'buruk',
    'parah': 'buruk',
    'ancur': 'buruk',
    'rusak': 'buruk'
}

def clean_text(text: str) -> str:
    """
    Basic text cleaning
    
    Args:
        text: Raw text to clean
        
    Returns:
        Cleaned text
    """
    try:
        if pd.isna(text) or text is None:
            return ""
            
        text = str(text)
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        
        # Remove email addresses
        text = re.sub(r'\S+@\S+', '', text)
        
        # Remove mentions and hashtags
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # Remove numbers
        text = re.sub(r'\d+', '', text)
        
        # Remove punctuation
        text = text.translate(str.maketrans('', '', string.punctuation))
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text.strip()
        
    except Exception as e:
        logger.error(f"Error cleaning text: {str(e)}")
        return str(text) if text else ""

def normalize_slang(text: str, slang_dict: Dict[str, str] = None) -> str:
    """
    Normalize Indonesian slang and abbreviations
    
    Args:
        text: Text to normalize
        slang_dict: Dictionary of slang mappings
        
    Returns:
        Normalized text
    """
    try:
        if slang_dict is None:
            slang_dict = SLANG_DICT
            
        words = text.split()
        normalized_words = []
        
        for word in words:
            normalized_word = slang_dict.get(word.lower(), word)
            normalized_words.append(normalized_word)
            
        return ' '.join(normalized_words)
        
    except Exception as e:
        logger.error(f"Error normalizing slang: {str(e)}")
        return text

def remove_stopwords(text: str, stopwords: set = None) -> str:
    """
    Remove Indonesian stopwords
    
    Args:
        text: Text to process
        stopwords: Set of stopwords to remove
        
    Returns:
        Text with stopwords removed
    """
    try:
        if stopwords is None:
            stopwords = INDONESIAN_STOPWORDS
            
        words = text.split()
        filtered_words = [word for word in words if word.lower() not in stopwords]
        
        return ' '.join(filtered_words)
        
    except Exception as e:
        logger.error(f"Error removing stopwords: {str(e)}")
        return text

def preprocess_text(text: str, 
                   clean: bool = True,
                   normalize: bool = True, 
                   remove_stops: bool = True) -> str:
    """
    Complete text preprocessing pipeline
    
    Args:
        text: Text to preprocess
        clean: Whether to apply basic cleaning
        normalize: Whether to normalize slang
        remove_stops: Whether to remove stopwords
        
    Returns:
        Preprocessed text
    """
    try:
        if pd.isna(text) or text is None:
            return ""
            
        processed_text = str(text)
        
        if clean:
            processed_text = clean_text(processed_text)
            
        if normalize:
            processed_text = normalize_slang(processed_text)
            
        if remove_stops:
            processed_text = remove_stopwords(processed_text)
            
        return processed_text.strip()
        
    except Exception as e:
        logger.error(f"Error preprocessing text: {str(e)}")
        return str(text) if text else ""

@st.cache_data(ttl=3600)
def preprocess_dataframe(df: pd.DataFrame, 
                        text_column: str,
                        clean: bool = True,
                        normalize: bool = True,
                        remove_stops: bool = True) -> pd.DataFrame:
    """
    Preprocess text in a dataframe
    
    Args:
        df: DataFrame to process
        text_column: Name of the text column
        clean: Whether to apply basic cleaning
        normalize: Whether to normalize slang
        remove_stops: Whether to remove stopwords
        
    Returns:
        DataFrame with preprocessed text
    """
    try:
        df_processed = df.copy()
        
        if text_column not in df_processed.columns:
            st.error(f"Column '{text_column}' not found in dataframe")
            return df_processed
            
        # Apply preprocessing
        df_processed['processed_text'] = df_processed[text_column].apply(
            lambda x: preprocess_text(x, clean, normalize, remove_stops)
        )
        
        # Remove empty texts
        df_processed = df_processed[df_processed['processed_text'].str.len() > 0]
        
        return df_processed
        
    except Exception as e:
        logger.error(f"Error preprocessing dataframe: {str(e)}")
        return df

def get_text_statistics(text: str) -> Dict[str, Any]:
    """
    Get statistics for a text
    
    Args:
        text: Text to analyze
        
    Returns:
        Dictionary with text statistics
    """
    try:
        if pd.isna(text) or text is None:
            return {
                'character_count': 0,
                'word_count': 0,
                'sentence_count': 0,
                'avg_word_length': 0
            }
            
        text = str(text)
        words = text.split()
        sentences = text.split('.')
        
        stats = {
            'character_count': len(text),
            'word_count': len(words),
            'sentence_count': len([s for s in sentences if s.strip()]),
            'avg_word_length': sum(len(word) for word in words) / len(words) if words else 0
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting text statistics: {str(e)}")
        return {
            'character_count': 0,
            'word_count': 0,
            'sentence_count': 0,
            'avg_word_length': 0
        }

def show_preprocessing_steps(text: str) -> Dict[str, str]:
    """
    Show step-by-step preprocessing results
    
    Args:
        text: Original text
        
    Returns:
        Dictionary with preprocessing steps
    """
    try:
        steps = {
            'original': str(text) if text else "",
            'cleaned': clean_text(text),
            'normalized': "",
            'no_stopwords': "",
            'final': ""
        }
        
        steps['normalized'] = normalize_slang(steps['cleaned'])
        steps['no_stopwords'] = remove_stopwords(steps['normalized'])
        steps['final'] = steps['no_stopwords']
        
        return steps
        
    except Exception as e:
        logger.error(f"Error showing preprocessing steps: {str(e)}")
        return {
            'original': str(text) if text else "",
            'cleaned': "",
            'normalized': "",
            'no_stopwords': "",
            'final': ""
        }
