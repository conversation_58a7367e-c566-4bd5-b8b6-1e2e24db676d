"""
Visualization utilities for the GoFood Sentiment Analysis Dashboard
"""

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud
import streamlit as st
from typing import Dict, List, Any, Optional, Tuple
import logging

from config.settings import CHART_CONFIG, UI_CONFIG
from config.constants import SENTIMENT_LABELS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sentiment_distribution_chart(df: pd.DataFrame, 
                                       sentiment_col: str = 'sentiment_label',
                                       chart_type: str = 'pie') -> go.Figure:
    """
    Create sentiment distribution chart
    
    Args:
        df: DataFrame with sentiment data
        sentiment_col: Name of sentiment column
        chart_type: Type of chart ('pie', 'bar', 'donut')
        
    Returns:
        Plotly figure
    """
    try:
        if sentiment_col not in df.columns:
            st.error(f"Column '{sentiment_col}' not found")
            return go.Figure()
            
        sentiment_counts = df[sentiment_col].value_counts()
        
        colors = [UI_CONFIG['success_color'], UI_CONFIG['danger_color'], UI_CONFIG['neutral_color']]
        
        if chart_type == 'pie':
            fig = px.pie(
                values=sentiment_counts.values,
                names=sentiment_counts.index,
                title="Distribusi Sentimen",
                color_discrete_sequence=colors
            )
        elif chart_type == 'donut':
            fig = px.pie(
                values=sentiment_counts.values,
                names=sentiment_counts.index,
                title="Distribusi Sentimen",
                color_discrete_sequence=colors,
                hole=0.4
            )
        else:  # bar chart
            fig = px.bar(
                x=sentiment_counts.index,
                y=sentiment_counts.values,
                title="Distribusi Sentimen",
                labels={'x': 'Sentimen', 'y': 'Jumlah'},
                color=sentiment_counts.index,
                color_discrete_sequence=colors
            )
            
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height']
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating sentiment distribution chart: {str(e)}")
        return go.Figure()

def create_rating_distribution_chart(df: pd.DataFrame, 
                                    rating_col: str = 'rating') -> go.Figure:
    """
    Create rating distribution chart
    
    Args:
        df: DataFrame with rating data
        rating_col: Name of rating column
        
    Returns:
        Plotly figure
    """
    try:
        if rating_col not in df.columns:
            st.error(f"Column '{rating_col}' not found")
            return go.Figure()
            
        rating_counts = df[rating_col].value_counts().sort_index()
        
        fig = px.bar(
            x=rating_counts.index,
            y=rating_counts.values,
            title="Distribusi Rating",
            labels={'x': 'Rating', 'y': 'Jumlah'},
            color=rating_counts.values,
            color_continuous_scale='viridis'
        )
        
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height'],
            xaxis=dict(tickmode='linear', tick0=1, dtick=1)
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating rating distribution chart: {str(e)}")
        return go.Figure()

def create_text_length_distribution(df: pd.DataFrame, 
                                  text_col: str = 'ulasan') -> go.Figure:
    """
    Create text length distribution chart
    
    Args:
        df: DataFrame with text data
        text_col: Name of text column
        
    Returns:
        Plotly figure
    """
    try:
        if text_col not in df.columns:
            st.error(f"Column '{text_col}' not found")
            return go.Figure()
            
        text_lengths = df[text_col].astype(str).str.len()
        
        fig = px.histogram(
            x=text_lengths,
            nbins=30,
            title="Distribusi Panjang Teks",
            labels={'x': 'Panjang Karakter', 'y': 'Frekuensi'},
            color_discrete_sequence=[UI_CONFIG['primary_color']]
        )
        
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height']
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating text length distribution: {str(e)}")
        return go.Figure()

def create_confusion_matrix_heatmap(cm: np.ndarray, 
                                  labels: List[str]) -> go.Figure:
    """
    Create confusion matrix heatmap
    
    Args:
        cm: Confusion matrix
        labels: Class labels
        
    Returns:
        Plotly figure
    """
    try:
        fig = px.imshow(
            cm,
            x=labels,
            y=labels,
            color_continuous_scale='Blues',
            title="Confusion Matrix",
            labels=dict(x="Predicted", y="Actual", color="Count"),
            text_auto=True
        )
        
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height']
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating confusion matrix heatmap: {str(e)}")
        return go.Figure()

def create_model_comparison_chart(df_results: pd.DataFrame) -> go.Figure:
    """
    Create model comparison chart
    
    Args:
        df_results: DataFrame with model comparison results
        
    Returns:
        Plotly figure
    """
    try:
        if df_results.empty:
            return go.Figure()
            
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        available_metrics = [m for m in metrics if m in df_results.columns]
        
        fig = go.Figure()
        
        for metric in available_metrics:
            fig.add_trace(go.Bar(
                name=metric.title(),
                x=df_results['model'],
                y=df_results[metric],
                text=df_results[metric].round(4),
                textposition='auto'
            ))
            
        fig.update_layout(
            title="Perbandingan Performa Model",
            xaxis_title="Model",
            yaxis_title="Score",
            barmode='group',
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height']
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating model comparison chart: {str(e)}")
        return go.Figure()

def create_feature_importance_chart(feature_importance: Dict[str, float], 
                                  top_n: int = 20) -> go.Figure:
    """
    Create feature importance chart
    
    Args:
        feature_importance: Dictionary of feature importance scores
        top_n: Number of top features to show
        
    Returns:
        Plotly figure
    """
    try:
        if not feature_importance:
            return go.Figure()
            
        # Sort features by importance
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:top_n]
        features, importance = zip(*sorted_features)
        
        fig = px.bar(
            x=list(importance),
            y=list(features),
            orientation='h',
            title=f"Top {top_n} Feature Importance",
            labels={'x': 'Importance Score', 'y': 'Features'},
            color=list(importance),
            color_continuous_scale='viridis'
        )
        
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=max(400, len(features) * 25)
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating feature importance chart: {str(e)}")
        return go.Figure()

def create_wordcloud(text_data: List[str], 
                    sentiment: str = None,
                    max_words: int = 100) -> Optional[WordCloud]:
    """
    Create word cloud from text data
    
    Args:
        text_data: List of texts
        sentiment: Sentiment filter (optional)
        max_words: Maximum number of words
        
    Returns:
        WordCloud object or None
    """
    try:
        if not text_data:
            return None
            
        # Combine all texts
        combined_text = ' '.join(text_data)
        
        if not combined_text.strip():
            return None
            
        # Create word cloud
        wordcloud = WordCloud(
            width=800,
            height=400,
            background_color='white',
            max_words=max_words,
            colormap='viridis',
            relative_scaling=0.5,
            random_state=42
        ).generate(combined_text)
        
        return wordcloud
        
    except Exception as e:
        logger.error(f"Error creating word cloud: {str(e)}")
        return None

def create_sentiment_trend_chart(df: pd.DataFrame,
                                date_col: str = 'date',
                                sentiment_col: str = 'sentiment_label') -> go.Figure:
    """
    Create sentiment trend over time chart
    
    Args:
        df: DataFrame with date and sentiment data
        date_col: Name of date column
        sentiment_col: Name of sentiment column
        
    Returns:
        Plotly figure
    """
    try:
        if date_col not in df.columns or sentiment_col not in df.columns:
            st.warning("Date or sentiment column not found for trend analysis")
            return go.Figure()
            
        # Convert date column to datetime
        df_trend = df.copy()
        df_trend[date_col] = pd.to_datetime(df_trend[date_col], errors='coerce')
        
        # Group by date and sentiment
        trend_data = df_trend.groupby([date_col, sentiment_col]).size().reset_index(name='count')
        
        fig = px.line(
            trend_data,
            x=date_col,
            y='count',
            color=sentiment_col,
            title="Trend Sentimen dari Waktu ke Waktu",
            labels={'count': 'Jumlah', date_col: 'Tanggal'},
            color_discrete_map={
                'Positif': UI_CONFIG['success_color'],
                'Negatif': UI_CONFIG['danger_color'],
                'Netral': UI_CONFIG['neutral_color']
            }
        )
        
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height']
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating sentiment trend chart: {str(e)}")
        return go.Figure()

def create_metrics_dashboard(metrics: Dict[str, float]) -> go.Figure:
    """
    Create metrics dashboard with gauge charts
    
    Args:
        metrics: Dictionary of metrics
        
    Returns:
        Plotly figure with subplots
    """
    try:
        if not metrics:
            return go.Figure()
            
        # Create subplots for gauge charts
        metric_names = list(metrics.keys())[:4]  # Limit to 4 metrics
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=[name.title() for name in metric_names],
            specs=[[{"type": "indicator"}, {"type": "indicator"}],
                   [{"type": "indicator"}, {"type": "indicator"}]]
        )
        
        positions = [(1, 1), (1, 2), (2, 1), (2, 2)]
        
        for i, (metric_name, value) in enumerate(zip(metric_names, [metrics[m] for m in metric_names])):
            if i >= 4:
                break
                
            row, col = positions[i]
            
            fig.add_trace(
                go.Indicator(
                    mode="gauge+number",
                    value=value,
                    domain={'x': [0, 1], 'y': [0, 1]},
                    title={'text': metric_name.title()},
                    gauge={
                        'axis': {'range': [None, 1]},
                        'bar': {'color': UI_CONFIG['primary_color']},
                        'steps': [
                            {'range': [0, 0.5], 'color': "lightgray"},
                            {'range': [0.5, 0.8], 'color': "gray"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 0.9
                        }
                    }
                ),
                row=row, col=col
            )
            
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=600,
            title="Model Performance Metrics"
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating metrics dashboard: {str(e)}")
        return go.Figure()

def create_prediction_confidence_chart(predictions: List[Dict[str, Any]]) -> go.Figure:
    """
    Create prediction confidence visualization
    
    Args:
        predictions: List of prediction results
        
    Returns:
        Plotly figure
    """
    try:
        if not predictions:
            return go.Figure()
            
        # Extract confidence scores and predictions
        confidences = []
        pred_labels = []
        
        for pred in predictions:
            if 'confidence' in pred and pred['confidence'] is not None:
                confidences.append(pred['confidence'])
                pred_labels.append(pred.get('prediction', 'Unknown'))
                
        if not confidences:
            return go.Figure()
            
        fig = px.histogram(
            x=confidences,
            color=pred_labels,
            title="Distribusi Confidence Score Prediksi",
            labels={'x': 'Confidence Score', 'y': 'Frekuensi'},
            nbins=20
        )
        
        fig.update_layout(
            template=CHART_CONFIG['template'],
            height=CHART_CONFIG['height']
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating prediction confidence chart: {str(e)}")
        return go.Figure()

def style_metric_card(title: str, value: str, delta: str = None) -> str:
    """
    Create styled metric card HTML
    
    Args:
        title: Metric title
        value: Metric value
        delta: Change value (optional)
        
    Returns:
        HTML string for metric card
    """
    delta_html = ""
    if delta:
        delta_color = UI_CONFIG['success_color'] if delta.startswith('+') else UI_CONFIG['danger_color']
        delta_html = f'<p style="color: {delta_color}; margin: 0; font-size: 14px;">{delta}</p>'
    
    return f"""
    <div style="
        background-color: {UI_CONFIG['background_color']};
        padding: 20px;
        border-radius: 10px;
        border: 1px solid {UI_CONFIG['neutral_color']};
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    ">
        <h3 style="color: {UI_CONFIG['primary_color']}; margin: 0; font-size: 16px;">{title}</h3>
        <h1 style="color: #212529; margin: 10px 0; font-size: 32px;">{value}</h1>
        {delta_html}
    </div>
    """
