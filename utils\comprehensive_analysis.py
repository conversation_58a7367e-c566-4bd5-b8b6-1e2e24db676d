"""
Comprehensive Analysis Module for GoFood Sentiment Analysis
Following the action plan for step-by-step preprocessing and feature extraction
"""

import pandas as pd
import numpy as np
import re
import string
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st
from typing import Dict, List, Tuple, Any
import logging
from collections import Counter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Indonesian stopwords and slang dictionary
INDONESIAN_STOPWORDS = {
    'ada', 'adalah', 'adanya', 'adapun', 'agak', 'agaknya', 'agar', 'akan', 'akankah', 'akhir',
    'akhiri', 'akhirnya', 'aku', 'akulah', 'amat', 'amatlah', 'anda', 'andalah', 'antar',
    'antara', 'antaranya', 'apa', 'apaan', 'apabila', 'apakah', 'apalagi', 'apatah', 'artinya',
    'asal', 'asalkan', 'atas', 'atau', 'ataukah', 'ataupun', 'awal', 'awalnya', 'bagai',
    'bagaikan', 'bagaimana', 'bagaimanakah', 'bagaimanapun', 'bagi', 'bagian', 'bahkan',
    'bahwa', 'bahwasanya', 'baik', 'bakal', 'bakalan', 'balik', 'banyak', 'bapak', 'baru',
    'bawah', 'beberapa', 'begini', 'beginian', 'beginikah', 'beginilah', 'begitu', 'begitukah',
    'begitulah', 'begitupun', 'bekerja', 'belakang', 'belakangan', 'belum', 'belumlah',
    'benar', 'benarkah', 'benarlah', 'berada', 'berakhir', 'berakhirlah', 'berakhirnya',
    'berapa', 'berapakah', 'berapalah', 'berapapun', 'berarti', 'berawal', 'berbagai',
    'berdatangan', 'beri', 'berikan', 'berikut', 'berikutnya', 'berjumlah', 'berkali',
    'berkata', 'berkehendak', 'berkeinginan', 'berkenaan', 'berlainan', 'berlalu', 'berlangsung',
    'berlebihan', 'bermacam', 'bermaksud', 'bermula', 'bersama', 'bersedia', 'bersiap',
    'bersiap-siap', 'bertanya', 'bertanya-tanya', 'berturut', 'berturut-turut', 'bertutur',
    'berujar', 'berupa', 'besar', 'betul', 'betulkah', 'biasa', 'biasanya', 'bila', 'bilakah',
    'bisa', 'bisakah', 'boleh', 'bolehkah', 'bukan', 'bukankah', 'bukanlah', 'bukannya',
    'bulan', 'bung', 'cara', 'caranya', 'cukup', 'cukupkah', 'cukuplah', 'cuma', 'dahulu',
    'dalam', 'dan', 'dapat', 'dari', 'daripada', 'datang', 'dekat', 'demi', 'demikian',
    'demikianlah', 'dengan', 'depan', 'di', 'dia', 'diakhiri', 'diakhirinya', 'dialah',
    'diantara', 'diantaranya', 'diberi', 'diberikan', 'diberikannya', 'dibuat', 'dibuatnya',
    'didapat', 'didatangkan', 'digunakan', 'diibaratkan', 'diibaratkannya', 'diingat',
    'diingatkan', 'diinginkan', 'dijawab', 'dijelaskan', 'dijelaskannya', 'dikarenakan',
    'dikatakan', 'dikatakannya', 'dikerjakan', 'diketahui', 'diketahuinya', 'dikira',
    'dilakukan', 'dilalui', 'dilihat', 'dimaksud', 'dimaksudkan', 'dimaksudkannya',
    'dimaksudnya', 'diminta', 'dimintai', 'dimisalkan', 'dimulai', 'dimulailah', 'dimulainya',
    'dimungkinkan', 'dini', 'dipastikan', 'diperbuat', 'diperbuatnya', 'dipergunakan',
    'diperkirakan', 'diperlihatkan', 'diperlukan', 'diperlukannya', 'dipersoalkan',
    'dipertanyakan', 'dipunyai', 'diri', 'dirinya', 'disampaikan', 'disebut', 'disebutkan',
    'disebutkannya', 'disini', 'disinilah', 'ditambahkan', 'ditandaskan', 'ditanya',
    'ditanyai', 'ditanyakan', 'ditegaskan', 'ditujukan', 'ditunjuk', 'ditunjuki',
    'ditunjukkan', 'ditunjukkannya', 'ditunjuknya', 'dituturkan', 'dituturkannya', 'diucapkan',
    'diucapkannya', 'diungkapkan', 'dong', 'dua', 'dulu', 'empat', 'enggak', 'enggaknya',
    'entah', 'entahlah', 'guna', 'gunakan', 'hal', 'hampir', 'hanya', 'hanyalah', 'hari',
    'harus', 'haruslah', 'harusnya', 'hendak', 'hendaklah', 'hendaknya', 'hingga', 'ia',
    'ialah', 'ibarat', 'ibaratkan', 'ibaratnya', 'ibu', 'ikut', 'ingat', 'ingat-ingat',
    'ingin', 'inginkah', 'inginkan', 'ini', 'inikah', 'inilah', 'itu', 'itukah', 'itulah',
    'jadi', 'jadilah', 'jadinya', 'jangan', 'jangankan', 'janganlah', 'jauh', 'jawab',
    'jawaban', 'jawabnya', 'jelas', 'jelaskan', 'jelaslah', 'jelasnya', 'jika', 'jikalau',
    'juga', 'jumlah', 'jumlahnya', 'justru', 'kala', 'kalau', 'kalaulah', 'kalaupun',
    'kalian', 'kami', 'kamilah', 'kamu', 'kamulah', 'kan', 'kapan', 'kapankah', 'kapanpun',
    'karena', 'karenanya', 'kasus', 'kata', 'katakan', 'katakanlah', 'katanya', 'ke', 'keadaan',
    'kebetulan', 'kecil', 'kedua', 'keduanya', 'keinginan', 'kelamaan', 'kelihatan',
    'kelihatannya', 'kelima', 'keluar', 'kembali', 'kemudian', 'kemungkinan', 'kemungkinannya',
    'kenapa', 'kepada', 'kepadanya', 'kesampaian', 'keseluruhan', 'keseluruhannya', 'ketika',
    'ketujuh', 'kira', 'kira-kira', 'kiranya', 'kita', 'kitalah', 'kok', 'kurang', 'lagi',
    'lagian', 'lah', 'lain', 'lainnya', 'lalu', 'lama', 'lamanya', 'lanjut', 'lanjutnya',
    'lebih', 'lewat', 'lima', 'luar', 'macam', 'maka', 'makanya', 'makin', 'malah', 'malahan',
    'mampu', 'mampukah', 'mana', 'manakala', 'manalagi', 'masa', 'masalah', 'masalahnya',
    'masih', 'masihkah', 'masing', 'masing-masing', 'mau', 'maupun', 'melainkan', 'melakukan',
    'melalui', 'melihat', 'melihatnya', 'memang', 'memastikan', 'memberi', 'memberikan',
    'membuat', 'memerlukan', 'memihak', 'meminta', 'memintakan', 'memisalkan', 'memperbuat',
    'mempergunakan', 'memperkirakan', 'memperlihatkan', 'mempersiapkan', 'mempersoalkan',
    'mempertanyakan', 'mempunyai', 'memulai', 'memungkinkan', 'menaiki', 'menambahkan',
    'menandaskan', 'menanti', 'menanya', 'menanyai', 'menanyakan', 'mendapat', 'mendapatkan',
    'mendatang', 'mendatangi', 'mendatangkan', 'menegaskan', 'mengakhiri', 'mengapa',
    'mengatakan', 'mengatakannya', 'mengenai', 'mengerjakan', 'mengetahui', 'menggunakan',
    'menghendaki', 'mengibaratkan', 'mengibaratkannya', 'mengingat', 'mengingatkan',
    'menginginkan', 'mengira', 'mengucapkan', 'mengucapkannya', 'mengungkapkan', 'menjadi',
    'menjawab', 'menjelaskan', 'menuju', 'menunjuk', 'menunjuki', 'menunjukkan', 'menunjuknya',
    'menurut', 'menuturkan', 'menyampaikan', 'menyangkut', 'menyatakan', 'menyebutkan',
    'menyeluruh', 'menyiapkan', 'merasa', 'mereka', 'merekalah', 'merupakan', 'meski',
    'meskipun', 'meyakini', 'meyakinkan', 'minta', 'mirip', 'misal', 'misalkan', 'misalnya',
    'mula', 'mulai', 'mulailah', 'mulanya', 'mungkin', 'mungkinkah', 'nah', 'naik', 'namun',
    'nanti', 'nantinya', 'nyaris', 'oleh', 'olehnya', 'pada', 'padahal', 'padanya', 'pak',
    'paling', 'panjang', 'pantas', 'para', 'pasti', 'pastilah', 'penting', 'pentingnya',
    'per', 'percuma', 'perlu', 'perlukah', 'perlunya', 'pernah', 'persoalan', 'pertama',
    'pertama-tama', 'pertanyaan', 'pertanyakan', 'pihak', 'pihaknya', 'pukul', 'pula',
    'pun', 'punya', 'punyakah', 'rah', 'rasa', 'rasanya', 'rata', 'rupanya', 'saat',
    'saatnya', 'saja', 'sajalah', 'saling', 'sama', 'sama-sama', 'sambil', 'sampai',
    'sampai-sampai', 'sampaikan', 'sana', 'sangat', 'sangatlah', 'satu', 'saya', 'sayalah',
    'se', 'sebab', 'sebabnya', 'sebagai', 'sebagaimana', 'sebagainya', 'sebagian', 'sebaik',
    'sebaik-baiknya', 'sebaiknya', 'sebaliknya', 'sebanyak', 'sebegini', 'sebegitu',
    'sebelum', 'sebelumnya', 'sebenarnya', 'seberapa', 'sebesar', 'sebetulnya', 'sebisanya',
    'sebuah', 'sebut', 'sebutlah', 'sebutnya', 'secara', 'secukupnya', 'sedang', 'sedangkan',
    'sedemikian', 'sedikit', 'sedikitnya', 'seenaknya', 'segala', 'segalanya', 'segera',
    'seharusnya', 'sehingga', 'seingat', 'sejak', 'sejauh', 'sejenak', 'sejumlah', 'sekadar',
    'sekadarnya', 'sekali', 'sekali-kali', 'sekalian', 'sekaligus', 'sekalipun', 'sekarang',
    'sekarang', 'sekecil', 'seketika', 'sekiranya', 'sekitar', 'sekitarnya', 'sekurang',
    'sekurang-kurangnya', 'sekurangnya', 'sela', 'selain', 'selaku', 'selalu', 'selama',
    'selama-lamanya', 'selamanya', 'selanjutnya', 'seluruh', 'seluruhnya', 'semacam',
    'semakin', 'semampu', 'semampunya', 'semasa', 'semasih', 'semata', 'semata-mata',
    'semaunya', 'sementara', 'semisal', 'semisalnya', 'sempat', 'semua', 'semuanya',
    'semula', 'sendiri', 'sendirian', 'sendirinya', 'seolah', 'seolah-olah', 'seorang',
    'sepanjang', 'sepantasnya', 'sepantasnyalah', 'seperlunya', 'seperti', 'sepertinya',
    'sepihak', 'sering', 'seringnya', 'serta', 'serupa', 'sesaat', 'sesama', 'sesampai',
    'sesegera', 'sesekali', 'seseorang', 'sesuai', 'sesuatu', 'sesuatunya', 'sesudah',
    'sesudahnya', 'setelah', 'setempat', 'setengah', 'seterusnya', 'setiap', 'setiba',
    'setibanya', 'setidak-tidaknya', 'setidaknya', 'setinggi', 'seusai', 'sewaktu', 'siap',
    'siapa', 'siapakah', 'siapapun', 'sih', 'sini', 'sinilah', 'soal', 'soalnya', 'suatu',
    'sudah', 'sudahkah', 'sudahlah', 'supaya', 'tadi', 'tadinya', 'tahu', 'tahun', 'tak',
    'tambah', 'tambahnya', 'tampak', 'tampaknya', 'tandas', 'tandasnya', 'tanpa', 'tanya',
    'tanyakan', 'tanyanya', 'tapi', 'tegas', 'tegasnya', 'telah', 'tempat', 'tengah',
    'tentang', 'tentu', 'tentulah', 'tentunya', 'tepat', 'terakhir', 'terasa', 'terbanyak',
    'terdahulu', 'terdapat', 'terdiri', 'terhadap', 'terhadapnya', 'teringat', 'teringat-ingat',
    'terjadi', 'terjadilah', 'terjadinya', 'terkira', 'terlalu', 'terlebih', 'terlihat',
    'termasuk', 'ternyata', 'tersampaikan', 'tersebut', 'tersebutlah', 'tertentu', 'tertuju',
    'terus', 'terutama', 'tetap', 'tetapi', 'tiap', 'tiba', 'tiba-tiba', 'tidak', 'tidakkah',
    'tidaklah', 'tiga', 'tinggi', 'toh', 'tunjuk', 'turut', 'tutur', 'tuturnya', 'ucap',
    'ucapnya', 'ujar', 'ujarnya', 'umum', 'umumnya', 'ungkap', 'ungkapnya', 'untuk',
    'usah', 'usai', 'waduh', 'wah', 'wahai', 'waktu', 'waktunya', 'walau', 'walaupun',
    'wong', 'yaitu', 'yakin', 'yakni', 'yang'
}

# Indonesian slang normalization dictionary
SLANG_DICT = {
    'gak': 'tidak', 'ga': 'tidak', 'ngga': 'tidak', 'nggak': 'tidak', 'gk': 'tidak',
    'gw': 'saya', 'gue': 'saya', 'ane': 'saya', 'w': 'saya',
    'lu': 'kamu', 'lo': 'kamu', 'elu': 'kamu', 'u': 'kamu',
    'yg': 'yang', 'dgn': 'dengan', 'utk': 'untuk', 'krn': 'karena', 'krna': 'karena',
    'tp': 'tapi', 'tpi': 'tapi', 'bgt': 'banget', 'bener': 'benar', 'bnr': 'benar',
    'udh': 'sudah', 'udah': 'sudah', 'dah': 'sudah', 'blm': 'belum', 'blom': 'belum',
    'jg': 'juga', 'jga': 'juga', 'emg': 'memang', 'emang': 'memang',
    'gmn': 'bagaimana', 'gimana': 'bagaimana', 'knp': 'kenapa', 'knapa': 'kenapa',
    'org': 'orang', 'orng': 'orang', 'hrs': 'harus', 'mst': 'harus',
    'bs': 'bisa', 'bsa': 'bisa', 'klo': 'kalau', 'kalo': 'kalau', 'kl': 'kalau',
    'dr': 'dari', 'dri': 'dari', 'sm': 'sama', 'sma': 'sama',
    'lg': 'lagi', 'lgi': 'lagi', 'aj': 'saja', 'aja': 'saja', 'doang': 'saja',
    'gt': 'begitu', 'gtu': 'begitu', 'gitu': 'begitu', 'bgtu': 'begitu',
    'mksd': 'maksud', 'mksud': 'maksud', 'mkny': 'makanya', 'makany': 'makanya',
    'skrg': 'sekarang', 'skrang': 'sekarang', 'skg': 'sekarang',
    'thx': 'terima kasih', 'thanks': 'terima kasih', 'makasih': 'terima kasih',
    'ok': 'oke', 'oke': 'oke', 'okey': 'oke', 'okay': 'oke',
    'mantap': 'bagus', 'mantul': 'bagus', 'keren': 'bagus',
    'jelek': 'buruk', 'parah': 'buruk', 'ancur': 'buruk'
}

def analyze_raw_data(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze raw data before preprocessing
    Following action plan Phase 1: Data Understanding
    """
    st.subheader("📊 Analisis Data Mentah (Sebelum Preprocessing)")
    
    analysis = {
        'basic_stats': {},
        'text_stats': {},
        'rating_analysis': {},
        'data_quality': {}
    }
    
    # Basic statistics
    analysis['basic_stats'] = {
        'total_records': len(df),
        'total_columns': len(df.columns),
        'column_names': df.columns.tolist(),
        'data_types': df.dtypes.to_dict(),
        'memory_usage': df.memory_usage(deep=True).sum() / 1024**2  # MB
    }
    
    # Text analysis for 'ulasan' column
    if 'ulasan' in df.columns:
        texts = df['ulasan'].astype(str)
        
        analysis['text_stats'] = {
            'total_texts': len(texts),
            'empty_texts': texts.str.strip().eq('').sum(),
            'avg_length': texts.str.len().mean(),
            'min_length': texts.str.len().min(),
            'max_length': texts.str.len().max(),
            'median_length': texts.str.len().median(),
            'total_words': texts.str.split().str.len().sum(),
            'avg_words_per_text': texts.str.split().str.len().mean(),
            'unique_texts': texts.nunique()
        }
        
        # Character distribution
        all_text = ' '.join(texts.tolist())
        char_freq = Counter(all_text.lower())
        analysis['text_stats']['most_common_chars'] = char_freq.most_common(10)
        
        # Word frequency analysis
        all_words = ' '.join(texts.tolist()).lower().split()
        word_freq = Counter(all_words)
        analysis['text_stats']['most_common_words'] = word_freq.most_common(20)
        
    # Rating analysis for 'nilai' column
    if 'nilai' in df.columns:
        ratings = df['nilai']
        
        analysis['rating_analysis'] = {
            'rating_distribution': ratings.value_counts().to_dict(),
            'avg_rating': ratings.mean(),
            'median_rating': ratings.median(),
            'rating_std': ratings.std(),
            'min_rating': ratings.min(),
            'max_rating': ratings.max(),
            'rating_range': ratings.max() - ratings.min()
        }
    
    # Data quality assessment
    missing_values = df.isnull().sum()
    duplicate_rows = df.duplicated().sum()
    
    analysis['data_quality'] = {
        'missing_values': missing_values.to_dict(),
        'total_missing': missing_values.sum(),
        'missing_percentage': (missing_values.sum() / (len(df) * len(df.columns))) * 100,
        'duplicate_rows': duplicate_rows,
        'duplicate_percentage': (duplicate_rows / len(df)) * 100,
        'completeness_score': ((len(df) * len(df.columns) - missing_values.sum()) / (len(df) * len(df.columns))) * 100
    }
    
    return analysis

def comprehensive_text_preprocessing(text: str) -> Dict[str, str]:
    """
    Comprehensive text preprocessing following action plan Phase 2
    Returns each step of preprocessing for analysis
    """
    steps = {}
    
    # Step 1: Original text
    steps['original'] = str(text) if text else ""
    
    # Step 2: Basic cleaning
    cleaned = str(text).lower() if text else ""
    # Remove URLs
    cleaned = re.sub(r'http\S+|www\S+|https\S+', '', cleaned, flags=re.MULTILINE)
    # Remove email addresses
    cleaned = re.sub(r'\S+@\S+', '', cleaned)
    # Remove mentions and hashtags
    cleaned = re.sub(r'@\w+|#\w+', '', cleaned)
    # Remove extra whitespace
    cleaned = ' '.join(cleaned.split())
    steps['basic_cleaning'] = cleaned
    
    # Step 3: Remove numbers and punctuation
    no_numbers = re.sub(r'\d+', '', cleaned)
    no_punct = no_numbers.translate(str.maketrans('', '', string.punctuation))
    steps['remove_numbers_punct'] = ' '.join(no_punct.split())
    
    # Step 4: Slang normalization
    words = steps['remove_numbers_punct'].split()
    normalized_words = [SLANG_DICT.get(word, word) for word in words]
    steps['slang_normalization'] = ' '.join(normalized_words)
    
    # Step 5: Remove stopwords
    words_no_stop = [word for word in normalized_words if word not in INDONESIAN_STOPWORDS]
    steps['stopword_removal'] = ' '.join(words_no_stop)
    
    # Step 6: Final cleaning
    final_text = ' '.join(steps['stopword_removal'].split())
    steps['final_processed'] = final_text
    
    return steps

@st.cache_data(ttl=3600)
def preprocess_dataset_comprehensive(df: pd.DataFrame, text_column: str = 'ulasan') -> pd.DataFrame:
    """
    Apply comprehensive preprocessing to entire dataset
    Following action plan Phase 2: Text Preprocessing
    """
    if text_column not in df.columns:
        st.error(f"Column '{text_column}' not found in dataset")
        return df
    
    df_processed = df.copy()
    
    # Apply preprocessing and store each step
    preprocessing_steps = []
    
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    for i, text in enumerate(df[text_column]):
        if i % 100 == 0:  # Update progress every 100 rows
            progress = i / len(df)
            progress_bar.progress(progress)
            status_text.text(f"Processing text {i+1}/{len(df)}")
        
        steps = comprehensive_text_preprocessing(text)
        preprocessing_steps.append(steps)
    
    progress_bar.progress(1.0)
    status_text.text("Preprocessing complete!")
    
    # Add preprocessing steps as new columns
    for step_name in ['basic_cleaning', 'remove_numbers_punct', 'slang_normalization', 
                      'stopword_removal', 'final_processed']:
        df_processed[f'{text_column}_{step_name}'] = [steps[step_name] for steps in preprocessing_steps]
    
    return df_processed

def analyze_preprocessing_impact(df_original: pd.DataFrame, df_processed: pd.DataFrame, 
                               text_column: str = 'ulasan') -> Dict[str, Any]:
    """
    Analyze the impact of preprocessing on the dataset
    Compare before and after preprocessing statistics
    """
    st.subheader("🔄 Analisis Dampak Preprocessing")
    
    impact_analysis = {}
    
    # Original text stats
    original_texts = df_original[text_column].astype(str)
    
    # Processed text stats (final step)
    processed_texts = df_processed[f'{text_column}_final_processed'].astype(str)
    
    # Length comparison
    original_lengths = original_texts.str.len()
    processed_lengths = processed_texts.str.len()
    
    impact_analysis['length_comparison'] = {
        'original_avg_length': original_lengths.mean(),
        'processed_avg_length': processed_lengths.mean(),
        'length_reduction_pct': ((original_lengths.mean() - processed_lengths.mean()) / original_lengths.mean()) * 100,
        'original_total_chars': original_lengths.sum(),
        'processed_total_chars': processed_lengths.sum()
    }
    
    # Word count comparison
    original_word_counts = original_texts.str.split().str.len()
    processed_word_counts = processed_texts.str.split().str.len()
    
    impact_analysis['word_comparison'] = {
        'original_avg_words': original_word_counts.mean(),
        'processed_avg_words': processed_word_counts.mean(),
        'word_reduction_pct': ((original_word_counts.mean() - processed_word_counts.mean()) / original_word_counts.mean()) * 100,
        'original_total_words': original_word_counts.sum(),
        'processed_total_words': processed_word_counts.sum()
    }
    
    # Vocabulary analysis
    original_vocab = set(' '.join(original_texts.tolist()).lower().split())
    processed_vocab = set(' '.join(processed_texts.tolist()).split())
    
    impact_analysis['vocabulary_comparison'] = {
        'original_vocab_size': len(original_vocab),
        'processed_vocab_size': len(processed_vocab),
        'vocab_reduction_pct': ((len(original_vocab) - len(processed_vocab)) / len(original_vocab)) * 100,
        'removed_words': len(original_vocab - processed_vocab),
        'retained_words': len(original_vocab & processed_vocab)
    }
    
    # Empty text analysis
    original_empty = original_texts.str.strip().eq('').sum()
    processed_empty = processed_texts.str.strip().eq('').sum()
    
    impact_analysis['empty_text_analysis'] = {
        'original_empty_count': original_empty,
        'processed_empty_count': processed_empty,
        'new_empty_texts': processed_empty - original_empty,
        'empty_increase_pct': ((processed_empty - original_empty) / len(df_original)) * 100 if original_empty != processed_empty else 0
    }
    
    return impact_analysis

def extract_features_comprehensive(df: pd.DataFrame, text_column: str = 'ulasan_final_processed') -> Dict[str, Any]:
    """
    Comprehensive feature extraction following action plan Phase 2: Feature Extraction
    Extract TF-IDF, N-gram, and other linguistic features
    """
    st.subheader("🔍 Feature Extraction Komprehensif")
    
    if text_column not in df.columns:
        st.error(f"Column '{text_column}' not found. Please run preprocessing first.")
        return {}
    
    from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
    
    features = {}
    texts = df[text_column].fillna('').astype(str)
    
    # Remove empty texts for feature extraction
    non_empty_texts = texts[texts.str.strip() != '']
    
    if len(non_empty_texts) == 0:
        st.warning("No non-empty texts found for feature extraction")
        return {}
    
    # 1. TF-IDF Features (Unigrams)
    st.write("📊 Extracting TF-IDF Unigram features...")
    tfidf_unigram = TfidfVectorizer(
        max_features=1000,
        ngram_range=(1, 1),
        min_df=2,
        max_df=0.95,
        stop_words=None  # Already removed in preprocessing
    )
    
    try:
        tfidf_unigram_matrix = tfidf_unigram.fit_transform(non_empty_texts)
        features['tfidf_unigram'] = {
            'vectorizer': tfidf_unigram,
            'matrix': tfidf_unigram_matrix,
            'feature_names': tfidf_unigram.get_feature_names_out(),
            'shape': tfidf_unigram_matrix.shape,
            'vocabulary_size': len(tfidf_unigram.vocabulary_)
        }
    except Exception as e:
        st.error(f"Error in TF-IDF unigram extraction: {e}")
    
    # 2. TF-IDF Features (Bigrams)
    st.write("📊 Extracting TF-IDF Bigram features...")
    tfidf_bigram = TfidfVectorizer(
        max_features=500,
        ngram_range=(2, 2),
        min_df=2,
        max_df=0.95
    )
    
    try:
        tfidf_bigram_matrix = tfidf_bigram.fit_transform(non_empty_texts)
        features['tfidf_bigram'] = {
            'vectorizer': tfidf_bigram,
            'matrix': tfidf_bigram_matrix,
            'feature_names': tfidf_bigram.get_feature_names_out(),
            'shape': tfidf_bigram_matrix.shape,
            'vocabulary_size': len(tfidf_bigram.vocabulary_)
        }
    except Exception as e:
        st.error(f"Error in TF-IDF bigram extraction: {e}")
    
    # 3. Count Vectorizer Features
    st.write("📊 Extracting Count Vectorizer features...")
    count_vectorizer = CountVectorizer(
        max_features=1000,
        ngram_range=(1, 1),
        min_df=2,
        max_df=0.95
    )
    
    try:
        count_matrix = count_vectorizer.fit_transform(non_empty_texts)
        features['count_vectorizer'] = {
            'vectorizer': count_vectorizer,
            'matrix': count_matrix,
            'feature_names': count_vectorizer.get_feature_names_out(),
            'shape': count_matrix.shape,
            'vocabulary_size': len(count_vectorizer.vocabulary_)
        }
    except Exception as e:
        st.error(f"Error in Count Vectorizer extraction: {e}")
    
    # 4. Linguistic Features
    st.write("📊 Extracting linguistic features...")
    linguistic_features = []
    
    for text in texts:
        text_str = str(text)
        features_dict = {
            'text_length': len(text_str),
            'word_count': len(text_str.split()),
            'avg_word_length': np.mean([len(word) for word in text_str.split()]) if text_str.split() else 0,
            'sentence_count': len([s for s in text_str.split('.') if s.strip()]),
            'exclamation_count': text_str.count('!'),
            'question_count': text_str.count('?'),
            'uppercase_ratio': sum(1 for c in text_str if c.isupper()) / len(text_str) if text_str else 0,
            'digit_count': sum(1 for c in text_str if c.isdigit()),
            'special_char_count': sum(1 for c in text_str if not c.isalnum() and not c.isspace())
        }
        linguistic_features.append(features_dict)
    
    features['linguistic'] = pd.DataFrame(linguistic_features)
    
    return features
